$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTYxNzE5LCJleHAiOjE3NTExNjUzMTl9.QEYGEFai2070jIVyqtSL26e40KGUNNyropKf9nR-U_c'
}

Write-Host "FINAL TEST: Admin Multiple Creation Without Errors"
Write-Host "=================================================="

# Get fresh token first
Write-Host "`nGetting fresh token..."
try {
    $loginData = @{
        email = "<EMAIL>"
        password = "password123"
    } | ConvertTo-Json

    $loginResult = Invoke-RestMethod -Uri 'http://localhost:8005/api/auth/login-quick' -Method POST -ContentType 'application/json' -Body $loginData
    $token = $loginResult.data.token
    $headers['Authorization'] = "Bearer $token"
    Write-Host "SUCCESS: Fresh token obtained"
} catch {
    Write-Host "ERROR: Could not get fresh token"
}

# Test Multiple Review Creation (JSON only - no image)
Write-Host "`nTesting Multiple Review Creation..."
$reviewSuccessCount = 0
for ($i = 1; $i -le 5; $i++) {
    $reviewData = @{
        name = "Test Customer $i"
        review = "This is test review number $i. Great service!"
        star = (Get-Random -Minimum 4 -Maximum 6)
        relationship = "client"
        eventType = "wedding"
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData
        Write-Host "SUCCESS: Review $i created - ID: $($result.data._id)"
        $reviewSuccessCount++
    } catch {
        Write-Host "ERROR: Review $i failed - $($_.Exception.Message)"
    }
}

# Test Multiple Gallery Creation (JSON only - no image)  
Write-Host "`nTesting Multiple Gallery Creation..."
$gallerySuccessCount = 0
for ($i = 1; $i -le 3; $i++) {
    $galleryData = @{
        title = "Test Gallery Item $i"
        description = "Test description for gallery item $i"
        category = "wedding"
        keywords = @("test", "gallery", "item$i")
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/gallery' -Method POST -Headers $headers -ContentType 'application/json' -Body $galleryData
        Write-Host "SUCCESS: Gallery $i created - ID: $($result.data._id)"
        $gallerySuccessCount++
    } catch {
        Write-Host "ERROR: Gallery $i failed - $($_.Exception.Message)"
    }
}

# Test Multiple Team Creation (JSON only - no image)
Write-Host "`nTesting Multiple Team Creation..."
$teamSuccessCount = 0
for ($i = 1; $i -le 3; $i++) {
    $teamData = @{
        name = "Team Member $i"
        position = "Event Coordinator $i"
        department = "Operations"
        bio = "Experienced team member number $i"
        sortOrder = $i
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/team' -Method POST -Headers $headers -ContentType 'application/json' -Body $teamData
        Write-Host "SUCCESS: Team Member $i created - ID: $($result.data._id)"
        $teamSuccessCount++
    } catch {
        Write-Host "ERROR: Team Member $i failed - $($_.Exception.Message)"
    }
}

# Test Multiple Hero Section Creation (JSON only - no image)
Write-Host "`nTesting Multiple Hero Section Creation..."
$heroSuccessCount = 0
for ($i = 1; $i -le 2; $i++) {
    $heroData = @{
        title = "Hero Section $i"
        subtitle = "Test subtitle $i"
        description = "Test description for hero section $i"
        buttonText = "Learn More $i"
        sortOrder = $i
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/hero-section' -Method POST -Headers $headers -ContentType 'application/json' -Body $heroData
        Write-Host "SUCCESS: Hero Section $i created - ID: $($result.data._id)"
        $heroSuccessCount++
    } catch {
        Write-Host "ERROR: Hero Section $i failed - $($_.Exception.Message)"
    }
}

Write-Host "`nFINAL RESULTS:"
Write-Host "=============="
Write-Host "Reviews Created: $reviewSuccessCount/5"
Write-Host "Gallery Items Created: $gallerySuccessCount/3"
Write-Host "Team Members Created: $teamSuccessCount/3"
Write-Host "Hero Sections Created: $heroSuccessCount/2"

$totalSuccess = $reviewSuccessCount + $gallerySuccessCount + $teamSuccessCount + $heroSuccessCount
$totalAttempts = 13

Write-Host "`nOVERALL SUCCESS: $totalSuccess/$totalAttempts items created"

if ($totalSuccess -gt 8) {
    Write-Host "EXCELLENT: Admin can create multiple items without duplicate errors!"
} elseif ($totalSuccess -gt 5) {
    Write-Host "GOOD: Most items created successfully"
} else {
    Write-Host "NEEDS ATTENTION: Many items failed to create"
}

Write-Host "`nTest Complete!"
