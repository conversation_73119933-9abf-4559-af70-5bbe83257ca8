const Team = require('../models/team.model');
const { StatusCodes } = require('http-status-codes');
const Joi = require('joi');

// Validation schemas
const teamSchema = Joi.object({
    name: Joi.string()
        .required()
        .max(100)
        .messages({
            'string.max': 'Name cannot exceed 100 characters',
            'any.required': 'Name is required'
        }),

    sortOrder: Joi.number().integer().min(0).default(0)
});

const updateTeamSchema = Joi.object({
    name: Joi.string()
        .max(100)
        .messages({
            'string.max': 'Name cannot exceed 100 characters'
        }),
    sortOrder: Joi.number().integer().min(0)
});

// Create Team Member
const createTeamMember = async (req, res) => {
    try {
        console.log('👥 Creating team member:', req.body);

        // Validate request body
        const { error, value } = teamSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Check if image is uploaded
        if (!req.file) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Image is required'
            });
        }

        // Create team member
        const teamData = {
            ...value,
            image: req.file.location
        };

        const teamMember = new Team(teamData);
        await teamMember.save();

        console.log('✅ Team member created:', teamMember._id);

        res.status(StatusCodes.CREATED).json({
            success: true,
            message: 'Team member created successfully',
            data: teamMember.getPublicData()
        });

    } catch (error) {
        console.error('❌ Create team member error:', error);

        // Allow admin to create multiple team members - no duplicate check needed

        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to create team member',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get All Team Members
const getAllTeamMembers = async (req, res) => {
    try {
        console.log('📋 Fetching all team members');

        const {
            page = 1,
            limit = 10,
            sortBy = 'sortOrder',
            sortOrder = 'asc',
            search
        } = req.query;

        // Build filter
        const filter = { isActive: true };

        if (search) {
            filter.name = new RegExp(search, 'i');
        }

        // Build sort
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query with pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        const [teamMembers, total] = await Promise.all([
            Team.find(filter)
                .sort(sort)
                .skip(skip)
                .limit(parseInt(limit)),
            Team.countDocuments(filter)
        ]);

        const totalPages = Math.ceil(total / parseInt(limit));

        console.log(`✅ Found ${teamMembers.length} team members`);

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                teamMembers: teamMembers.map(member => member.getPublicData()),
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalItems: total,
                    itemsPerPage: parseInt(limit),
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1
                }
            }
        });

    } catch (error) {
        console.error('❌ Get all team members error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch team members',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get Team Member by ID
const getTeamMemberById = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🔍 Fetching team member by ID:', id);

        // Find by MongoDB ObjectId only
        let teamMember = await Team.findOne({
            _id: id,
            isActive: true
        });

        if (!teamMember) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Team member not found'
            });
        }

        console.log('✅ Team member found:', teamMember._id);

        res.status(StatusCodes.OK).json({
            success: true,
            data: teamMember.getPublicData()
        });

    } catch (error) {
        console.error('❌ Get team member by ID error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch team member',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Update Team Member
const updateTeamMember = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('📝 Updating team member:', id);

        // Validate request body
        const { error, value } = updateTeamSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Find team member by ObjectId
        let teamMember = await Team.findOne({
            _id: id,
            isActive: true
        });

        if (!teamMember) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Team member not found'
            });
        }

        // Update fields
        Object.keys(value).forEach(key => {
            teamMember[key] = value[key];
        });

        // Update image if new one is uploaded
        if (req.file) {
            teamMember.image = req.file.location;
        }

        await teamMember.save();

        console.log('✅ Team member updated:', teamMember._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Team member updated successfully',
            data: teamMember.getPublicData()
        });

    } catch (error) {
        console.error('❌ Update team member error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to update team member',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Delete Team Member
const deleteTeamMember = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🗑️ Deleting team member:', id);

        // Find and soft delete team member
        const teamMember = await Team.findOneAndUpdate(
            {
                _id: id,
                isActive: true
            },
            { isActive: false },
            { new: true }
        );

        if (!teamMember) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Team member not found'
            });
        }

        console.log('✅ Team member deleted:', teamMember._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Team member deleted successfully'
        });

    } catch (error) {
        console.error('❌ Delete team member error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to delete team member',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

module.exports = {
    createTeamMember,
    getAllTeamMembers,
    getTeamMemberById,
    updateTeamMember,
    deleteTeamMember
};
