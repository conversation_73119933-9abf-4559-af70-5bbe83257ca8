const mongoose = require("mongoose");
const AutoIncrement = require("mongoose-sequence")(mongoose);

const blogSchema = new mongoose.Schema(
  {
    id: { type: Number, unique: true },
    title: {
      type: String,
      required: [true, "Title is required"],
      trim: true,
      maxlength: [200, "Title cannot exceed 200 characters"]
    },
    description: {
      type: String,
      required: [true, "Description is required"],
      trim: false, // Don't trim to preserve formatting
      maxlength: [10000, "Description cannot exceed 10000 characters"]
    },
    imageUrl: {
      type: String,
      required: false,
      default: null
    },
    category: [{
      type: String,
      required: true,
      trim: true,
      lowercase: true
    }],
    keywords: [{
      type: String,
      trim: true,
      lowercase: true
    }],
    views: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true,
    toJSON: {
      transform: (doc, ret) => {
        // Convert timestamps to IST
        ret.createdAt = new Date(ret.createdAt).toLocaleString("en-IN", { timeZone: "Asia/Kolkata" });
        ret.updatedAt = new Date(ret.updatedAt).toLocaleString("en-IN", { timeZone: "Asia/Kolkata" });
        return ret;
      }
    }
  }
);

// Index for better search performance
blogSchema.index({ title: 'text', description: 'text', keywords: 'text' });
blogSchema.index({ category: 1 });
blogSchema.index({ createdAt: -1 });

blogSchema.plugin(AutoIncrement, { inc_field: "id" });

module.exports = mongoose.model("Blog", blogSchema);
