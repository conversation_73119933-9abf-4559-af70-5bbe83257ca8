# ⭐ Reviews API Documentation

## Overview
Complete API documentation for Reviews management in ParvEvent Backend. Reviews represent customer testimonials and feedback for events and services.

## Base URL
```
http://localhost:8005/api/reviews
```

## Authentication
All endpoints require admin authentication:
```
Authorization: Bearer <JWT_TOKEN>
```

---

## 📋 **API Endpoints**

### 1. Create Review
**POST** `/api/reviews`

Creates a new customer review with optional image upload.

**Content-Type:** `multipart/form-data`

**Form Fields:**
```javascript
{
  "name": "<PERSON>",                      // Required, max 100 chars
  "review": "Amazing service! Highly recommend...", // Required, max 1000 chars
  "star": 5,                                   // Required, 1-5 integer
  "relationship": "client",                    // Required, predefined values
  "eventType": "wedding",                      // Required, predefined values
  "eventDate": "2023-12-15",                  // Optional, ISO date
  "location": "Mumbai",                        // Optional, max 100 chars
  "serviceUsed": "Full Event Planning",       // Optional, max 200 chars
  "highlights": [                              // Optional array
    "Professional team",
    "Beautiful decoration",
    "Excellent coordination"
  ],
  "wouldRecommend": true,                      // Optional, default true
  "isPublic": true,                           // Optional, default true
  "isFeatured": false,                        // Optional, default false
  "sortOrder": 1                              // Optional, default 0
}

// File upload (optional):
image: File                    // Customer or event image
```

**Relationship Types:**
- client, vendor, partner, colleague, friend, family, other

**Event Types:**
- wedding, corporate, birthday, anniversary, engagement
- reception, conference, seminar, party, other

**Response:**
```javascript
{
  "success": true,
  "message": "Review created successfully",
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "Sarah Johnson",
    "review": "Amazing service! The team was professional and delivered exactly what we wanted for our wedding. Every detail was perfect!",
    "star": 5,
    "relationship": "client",
    "eventType": "wedding",
    "eventDate": "2023-12-15T00:00:00.000Z",
    "location": "Mumbai",
    "serviceUsed": "Full Event Planning",
    "image": "https://s3.amazonaws.com/bucket/reviews/sarah-wedding.jpg",
    "highlights": [
      "Professional team",
      "Beautiful decoration", 
      "Excellent coordination"
    ],
    "wouldRecommend": true,
    "isPublic": true,
    "isFeatured": false,
    "sortOrder": 1,
    "isActive": true,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

### 2. Get All Reviews
**GET** `/api/reviews`

Retrieves all active reviews with pagination and filtering.

**Query Parameters:**
```
?page=1                    // Page number (default: 1)
&limit=10                  // Items per page (default: 10, max: 100)
&sortBy=createdAt          // Sort field (default: createdAt)
&sortOrder=desc            // Sort order: asc/desc (default: desc)
&star=5                    // Filter by star rating
&relationship=client       // Filter by relationship type
&eventType=wedding         // Filter by event type
&location=Mumbai           // Filter by location
&isFeatured=true           // Filter featured reviews
&isPublic=true             // Filter public reviews
&wouldRecommend=true       // Filter recommended reviews
&search=amazing            // Search in name, review, highlights
```

**Response:**
```javascript
{
  "success": true,
  "data": {
    "reviews": [
      {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
        "name": "Sarah Johnson",
        "review": "Amazing service! The team was professional...",
        "star": 5,
        "relationship": "client",
        "eventType": "wedding",
        "eventDate": "2023-12-15T00:00:00.000Z",
        "location": "Mumbai",
        "serviceUsed": "Full Event Planning",
        "image": "https://s3.amazonaws.com/bucket/reviews/sarah-wedding.jpg",
        "highlights": [
          "Professional team",
          "Beautiful decoration"
        ],
        "wouldRecommend": true,
        "isPublic": true,
        "isFeatured": true,
        "sortOrder": 1,
        "createdAt": "2023-12-01T10:00:00.000Z"
      },
      {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b4",
        "name": "Raj Patel",
        "review": "Excellent corporate event management...",
        "star": 4,
        "relationship": "client",
        "eventType": "corporate",
        "location": "Delhi",
        "serviceUsed": "Corporate Event Planning",
        "wouldRecommend": true,
        "isPublic": true,
        "isFeatured": false,
        "sortOrder": 2,
        "createdAt": "2023-11-28T09:00:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 6,
      "totalItems": 55,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

### 3. Get Review by ID
**GET** `/api/reviews/:id`

Retrieves a specific review by MongoDB ObjectId.

**Parameters:**
- `id` - MongoDB ObjectId

**Response:**
```javascript
{
  "success": true,
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "Sarah Johnson",
    "review": "Amazing service! The team was professional and delivered exactly what we wanted for our wedding. Every detail was perfect and the coordination was seamless. I would definitely recommend ParvEvent to anyone looking for exceptional event management.",
    "star": 5,
    "relationship": "client",
    "eventType": "wedding",
    "eventDate": "2023-12-15T00:00:00.000Z",
    "location": "Mumbai",
    "serviceUsed": "Full Event Planning",
    "image": "https://s3.amazonaws.com/bucket/reviews/sarah-wedding.jpg",
    "highlights": [
      "Professional team",
      "Beautiful decoration",
      "Excellent coordination",
      "Timely execution",
      "Great communication"
    ],
    "wouldRecommend": true,
    "isPublic": true,
    "isFeatured": true,
    "sortOrder": 1,
    "isActive": true,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

### 4. Update Review
**PUT** `/api/reviews/:id`

Updates an existing review. Supports both JSON and multipart/form-data.

**Content-Type:** `multipart/form-data` or `application/json`

**Parameters:**
- `id` - MongoDB ObjectId

**Form Fields:** (All optional)
```javascript
{
  "name": "Sarah Johnson Updated",
  "review": "Updated review text with more details...",
  "star": 4,
  "relationship": "client",
  "eventType": "wedding",
  "eventDate": "2023-12-15",
  "location": "Mumbai",
  "serviceUsed": "Premium Event Planning",
  "highlights": [
    "Updated highlight 1",
    "Updated highlight 2"
  ],
  "wouldRecommend": true,
  "isPublic": true,
  "isFeatured": true,
  "sortOrder": 1
}

// File upload (optional):
image: File                    // New review image
```

**Response:**
```javascript
{
  "success": true,
  "message": "Review updated successfully",
  "data": {
    // Updated review object
  }
}
```

### 5. Delete Review
**DELETE** `/api/reviews/:id`

Soft deletes a review (sets isActive: false).

**Parameters:**
- `id` - MongoDB ObjectId

**Response:**
```javascript
{
  "success": true,
  "message": "Review deleted successfully",
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "Sarah Johnson",
    "isActive": false,
    "updatedAt": "2023-12-01T11:00:00.000Z"
  }
}
```

### 6. Get Review Statistics
**GET** `/api/reviews/statistics`

Retrieves comprehensive review statistics for admin dashboard.

**Response:**
```javascript
{
  "success": true,
  "data": {
    "totalReviews": 55,
    "averageRating": 4.6,
    "ratingDistribution": {
      "5": 35,
      "4": 15,
      "3": 4,
      "2": 1,
      "1": 0
    },
    "eventTypeStats": [
      {
        "_id": "wedding",
        "count": 25,
        "averageRating": 4.8
      },
      {
        "_id": "corporate",
        "count": 15,
        "averageRating": 4.5
      }
    ],
    "relationshipStats": [
      {
        "_id": "client",
        "count": 45
      },
      {
        "_id": "partner",
        "count": 8
      }
    ],
    "featuredCount": 12,
    "publicCount": 50,
    "recommendationRate": 96.4,
    "recentReviews": 8,
    "topLocations": [
      {
        "_id": "Mumbai",
        "count": 20
      },
      {
        "_id": "Delhi",
        "count": 15
      }
    ]
  }
}
```

---

## 🚨 **Error Responses**

### Validation Error (400)
```javascript
{
  "success": false,
  "message": "Validation error",
  "errors": [
    "Name is required",
    "Review text is required",
    "Star rating must be between 1 and 5",
    "Review cannot exceed 1000 characters"
  ]
}
```

### Not Found (404)
```javascript
{
  "success": false,
  "message": "Review not found"
}
```

### Server Error (500)
```javascript
{
  "success": false,
  "message": "Failed to create review",
  "debug": "Detailed error message (development only)"
}
```

---

## 📝 **Usage Examples**

### Frontend Integration
```javascript
// Create review with image
const formData = new FormData();
formData.append('name', 'Sarah Johnson');
formData.append('review', 'Amazing service! Highly recommend...');
formData.append('star', '5');
formData.append('relationship', 'client');
formData.append('eventType', 'wedding');
formData.append('eventDate', '2023-12-15');
formData.append('location', 'Mumbai');
formData.append('serviceUsed', 'Full Event Planning');
formData.append('highlights', JSON.stringify(['Professional team', 'Beautiful decoration']));
formData.append('wouldRecommend', 'true');
formData.append('isPublic', 'true');
formData.append('image', reviewImageFile);

const response = await fetch('/api/reviews', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Get reviews with filtering
const reviews = await fetch('/api/reviews?star=5&eventType=wedding&isFeatured=true&page=1&limit=10', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Update review
const updateData = {
  star: 4,
  isFeatured: true,
  highlights: ['Updated highlight 1', 'Updated highlight 2']
};

const updateResponse = await fetch(`/api/reviews/${reviewId}`, {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(updateData)
});
```

### Admin Features
- ✅ Create unlimited customer reviews
- ✅ Upload customer or event images
- ✅ Comprehensive review management
- ✅ Star rating system (1-5)
- ✅ Event type categorization
- ✅ Relationship tracking
- ✅ Featured reviews management
- ✅ Public/private review control
- ✅ Advanced filtering and search
- ✅ Review statistics and analytics
- ✅ Recommendation tracking

---

## 🔧 **Technical Notes**

- **File Upload**: Uses AWS S3 for image storage
- **Image Types**: Supports JPG, PNG, GIF, WebP
- **File Size**: Maximum 5MB per image
- **Optional Image**: Review image is optional
- **Database**: MongoDB with ObjectId
- **Validation**: Joi schema validation
- **Security**: JWT authentication required
- **Soft Delete**: Reviews are never permanently deleted
- **Search**: Full-text search in name, review text, and highlights
- **Statistics**: Real-time analytics and reporting
- **Rating System**: 5-star rating with decimal averages

---

## 🎯 **Business Logic**

### Admin Capabilities
- **Unlimited Creation**: Admin can create multiple reviews for testimonials
- **No Duplicate Restrictions**: Multiple reviews from same customer allowed
- **Content Management**: Full control over review content and visibility
- **Featured Management**: Promote important reviews
- **Analytics**: Comprehensive review statistics
- **Quality Control**: Manage public visibility

### Display Features
- **Star Ratings**: Visual star display with averages
- **Event Categorization**: Group reviews by event types
- **Customer Relationships**: Track different customer types
- **Recommendation Tracking**: Monitor recommendation rates
- **Location Analytics**: Geographic review distribution
- **Highlight System**: Key points extraction

---

*Last Updated: December 2024*
