const HeroSection = require('../models/heroSection.model');
const { StatusCodes } = require('http-status-codes');
const Joi = require('joi');

// Validation schemas
const heroSectionSchema = Joi.object({
    title: Joi.string()
        .max(200)
        .allow('')
        .messages({
            'string.max': 'Title cannot exceed 200 characters'
        }),
    subtitle: Joi.string()
        .max(300)
        .allow('')
        .messages({
            'string.max': 'Subtitle cannot exceed 300 characters'
        }),
    description: Joi.string()
        .max(500)
        .allow('')
        .messages({
            'string.max': 'Description cannot exceed 500 characters'
        }),
    buttonText: Joi.string()
        .max(50)
        .default('Learn More')
        .messages({
            'string.max': 'Button text cannot exceed 50 characters'
        }),
    buttonLink: Joi.string()
        .default('#'),

    sortOrder: Joi.number()
        .integer()
        .min(0)
        .default(0),
    isPrimary: Joi.boolean()
        .default(false),
    displayDuration: Joi.number()
        .integer()
        .min(1000)
        .max(30000)
        .default(5000)
        .messages({
            'number.min': 'Display duration must be at least 1 second',
            'number.max': 'Display duration cannot exceed 30 seconds'
        }),
    isActive: Joi.boolean().default(true),
    sortOrder: Joi.number().integer().min(0).default(0)
});

const updateHeroSectionSchema = Joi.object({
    title: Joi.string()
        .max(200)
        .messages({
            'string.max': 'Title cannot exceed 200 characters'
        }),
    subtitle: Joi.string()
        .max(300)
        .allow('')
        .messages({
            'string.max': 'Subtitle cannot exceed 300 characters'
        }),
    description: Joi.string()
        .max(500)
        .allow('')
        .messages({
            'string.max': 'Description cannot exceed 500 characters'
        }),
    buttonText: Joi.string()
        .max(50)
        .messages({
            'string.max': 'Button text cannot exceed 50 characters'
        }),
    buttonLink: Joi.string(),
    sortOrder: Joi.number()
        .integer()
        .min(0),
    isPrimary: Joi.boolean(),
    displayDuration: Joi.number()
        .integer()
        .min(1000)
        .max(30000)
        .messages({
            'number.min': 'Display duration must be at least 1 second',
            'number.max': 'Display duration cannot exceed 30 seconds'
        }),
    isActive: Joi.boolean()
});

// Create hero section
const createHeroSection = async (req, res) => {
    try {
        console.log('🎯 Creating hero section:', req.body);
        
        // Validate request body
        const { error, value } = heroSectionSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Check if image is uploaded
        if (!req.file) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Image is required'
            });
        }

        // Create hero section
        const heroData = {
            ...value,
            image: req.file.location
        };

        const heroSection = new HeroSection(heroData);
        await heroSection.save();

        console.log('✅ Hero section created:', heroSection._id);

        res.status(StatusCodes.CREATED).json({
            success: true,
            message: 'Hero section created successfully',
            data: heroSection.getPublicData()
        });

    } catch (error) {
        console.error('❌ Create hero section error:', error);
        
        // Allow admin to create multiple hero sections - no duplicate check needed

        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to create hero section',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get all hero sections
const getAllHeroSections = async (req, res) => {
    try {
        console.log('📋 Fetching all hero sections');
        
        const { 
            page = 1, 
            limit = 10, 
            sortBy = 'sortOrder',
            sortOrder = 'asc',
            activeOnly = 'true'
        } = req.query;

        // Build filter
        const filter = {};
        if (activeOnly === 'true') {
            filter.isActive = true;
        }

        // Build sort
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query with pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        
        const [heroSections, total] = await Promise.all([
            HeroSection.find(filter)
                .sort(sort)
                .skip(skip)
                .limit(parseInt(limit)),
            HeroSection.countDocuments(filter)
        ]);

        const totalPages = Math.ceil(total / parseInt(limit));

        console.log(`✅ Found ${heroSections.length} hero sections`);

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                heroSections: heroSections.map(hero => hero.getPublicData()),
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalItems: total,
                    itemsPerPage: parseInt(limit),
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1
                }
            }
        });

    } catch (error) {
        console.error('❌ Get all hero sections error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch hero sections',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get hero section by ID
const getHeroSectionById = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🔍 Fetching hero section by ID:', id);

        // Find by MongoDB ObjectId only
        let heroSection = await HeroSection.findOne({
            _id: id,
            isActive: true
        });

        if (!heroSection) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Hero section not found'
            });
        }

        console.log('✅ Hero section found:', heroSection._id);

        res.status(StatusCodes.OK).json({
            success: true,
            data: heroSection.getPublicData()
        });

    } catch (error) {
        console.error('❌ Get hero section by ID error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch hero section',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Update hero section
const updateHeroSection = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('📝 Updating hero section:', id);

        // Validate request body
        const { error, value } = updateHeroSectionSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Find hero section by ObjectId
        let heroSection = await HeroSection.findOne({
            _id: id,
            isActive: true
        });

        if (!heroSection) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Hero section not found'
            });
        }

        // Update fields
        Object.keys(value).forEach(key => {
            heroSection[key] = value[key];
        });

        // Update image if new one is uploaded
        if (req.file) {
            heroSection.image = req.file.location;
        }

        await heroSection.save();

        console.log('✅ Hero section updated:', heroSection._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Hero section updated successfully',
            data: heroSection.getPublicData()
        });

    } catch (error) {
        console.error('❌ Update hero section error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to update hero section',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Delete hero section
const deleteHeroSection = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🗑️ Deleting hero section:', id);

        // Find and soft delete hero section by ObjectId
        const heroSection = await HeroSection.findOneAndUpdate(
            {
                _id: id,
                isActive: true
            },
            { isActive: false, isPrimary: false },
            { new: true }
        );

        if (!heroSection) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Hero section not found'
            });
        }

        console.log('✅ Hero section deleted:', heroSection._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Hero section deleted successfully'
        });

    } catch (error) {
        console.error('❌ Delete hero section error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to delete hero section',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get primary hero section
const getPrimaryHeroSection = async (req, res) => {
    try {
        console.log('🎯 Fetching primary hero section');

        const heroSection = await HeroSection.getPrimaryHero();

        if (!heroSection) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'No primary hero section found'
            });
        }

        res.status(StatusCodes.OK).json({
            success: true,
            data: heroSection.getPublicData()
        });

    } catch (error) {
        console.error('❌ Get primary hero section error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch primary hero section',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

module.exports = {
    createHeroSection,
    getAllHeroSections,
    getHeroSectionById,
    updateHeroSection,
    deleteHeroSection,
    getPrimaryHeroSection
};
