const express = require('express');
const router = express.Router();
const {
    createGallery,
    getAllGallery,
    getGalleryById,
    updateGallery,
    deleteGallery
} = require('../controllers/gallery.controller');
const { galleryUploadSingle } = require('../config/awsclouds3');
const authMiddleware = require('../middleware/auth.middleware');

// Apply security headers to all gallery routes
router.use(authMiddleware.securityHeaders);

// Public routes (no authentication required)
router.get('/', getAllGallery);
router.get('/:id', getGalleryById);

// Protected routes (authentication required)
router.post('/', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    galleryUploadSingle('image'),
    createGallery
);

router.put('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    galleryUploadSingle('image'),
    updateGallery
);

router.delete('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    deleteGallery
);

module.exports = router;
