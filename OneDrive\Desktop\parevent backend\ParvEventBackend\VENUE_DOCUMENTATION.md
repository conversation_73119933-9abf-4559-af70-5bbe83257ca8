# 🏛️ Venue & Venue Booking System - Complete Documentation

## Overview
Complete venue management and booking system with simplified venue listings and comprehensive booking forms for event management.

## 🏗️ System Architecture

### Venue Management System
- **Model**: `models/venue.model.js` - Venue listings
- **Controller**: `controllers/venue.controller.js` - Venue CRUD operations
- **Routes**: `routes/venue.route.js` - Venue API endpoints

### Venue Booking System
- **Model**: `models/venueForm.model.js` - Booking requests
- **Controller**: `controllers/venueForm.controller.js` - Booking management
- **Routes**: `routes/venueForm.route.js` - Booking API endpoints

## 📊 Database Schemas

### Venue Model (Simplified)
```javascript
{
  _id: ObjectId,
  name: String (required, max 200 chars),
  image: String (required, S3 URL),
  venueType: String (enum: banquet-hall, outdoor, resort, hotel, farmhouse, palace, garden, beach, other),
  location: String (required, max 500 chars),
  capacity: Number (1-15000),
  seats: Number (1-10000),
  isActive: Boolean (default: true),
  sortOrder: Number (default: 0),
  createdAt: Date,
  updatedAt: Date
}
```

### Venue Booking Model (Simplified)
```javascript
{
  _id: ObjectId,
  fullName: String (required, max 100 chars),
  email: String (required, validated),
  phoneNumber: String (required, 10-digit Indian number),
  venueId: ObjectId (ref: Venue, required),
  venueName: String (required, max 200 chars),
  dateOfPlan: Date (required, future date),
  message: String (required, max 1000 chars),
  status: String (enum: new, in-progress, resolved, closed),
  priority: String (enum: low, medium, high, urgent),
  isActive: Boolean (default: true),
  createdAt: Date,
  updatedAt: Date
}
```

## 🔗 API Endpoints

### Venue Management APIs

#### 1. Get All Venues (Public)
```http
GET /venues?page=1&limit=10&venueType=banquet-hall&search=mumbai&sortBy=name&sortOrder=asc
```

**Response:**
```javascript
{
  "success": true,
  "data": {
    "venues": [
      {
        "_id": "ObjectId",
        "name": "String",
        "image": "String (S3 URL)",
        "venueType": "String",
        "location": "String",
        "capacity": "Number",
        "seats": "Number",
        "isActive": "Boolean",
        "sortOrder": "Number",
        "createdAt": "Date",
        "updatedAt": "Date"
      }
    ],
    "pagination": {
      "currentPage": "Number",
      "totalPages": "Number",
      "totalItems": "Number",
      "itemsPerPage": "Number",
      "hasNextPage": "Boolean",
      "hasPrevPage": "Boolean"
    }
  }
}
```

#### 2. Get Venue by ID (Public)
```http
GET /venues/:id
```

#### 3. Create Venue (Admin)
```http
POST /venues
Content-Type: multipart/form-data
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:**
```javascript
{
  "name": "String (required, max 200 chars)",
  "venueType": "String (required, enum values)",
  "location": "String (required, max 500 chars)",
  "capacity": "Number (required, 1-15000)",
  "seats": "Number (required, 1-10000)",
  "sortOrder": "Number (optional, default: 0)",
  "image": "File (required, image upload)"
}
```

#### 4. Update Venue (Admin)
```http
PUT /venues/:id
Authorization: Bearer <JWT_TOKEN>
```

#### 5. Delete Venue (Admin)
```http
DELETE /venues/:id
Authorization: Bearer <JWT_TOKEN>
```

### Venue Booking APIs

#### 6. Create Booking (Public)
```http
POST /venue-bookings
Content-Type: application/json
```

**Request Body:**
```javascript
{
  "fullName": "String (required, max 100 chars)",
  "email": "String (required, valid email)",
  "phoneNumber": "String (required, 10-digit Indian number)",
  "venueId": "ObjectId (required, ref to Venue)",
  "venueName": "String (required, max 200 chars)",
  "dateOfPlan": "Date (required, future date)",
  "message": "String (required, max 1000 chars)"
}
```

**Response:**
```javascript
{
  "success": true,
  "message": "Venue booking request submitted successfully",
  "data": {
    "_id": "ObjectId",
    "fullName": "String",
    "email": "String",
    "phoneNumber": "String",
    "venueId": "ObjectId",
    "venueName": "String",
    "dateOfPlan": "Date",
    "message": "String",
    "status": "new",
    "priority": "medium",
    "createdAt": "Date",
    "updatedAt": "Date"
  }
}
```

#### 7. Get All Bookings (Admin)
```http
GET /venue-bookings?page=1&limit=10&status=pending&priority=high&eventType=wedding
Authorization: Bearer <JWT_TOKEN>
```

#### 8. Get Booking by ID (Admin)
```http
GET /venue-bookings/:id
Authorization: Bearer <JWT_TOKEN>
```

#### 9. Update Booking (Admin)
```http
PUT /venue-bookings/:id
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:**
```javascript
{
  "status": "resolved",
  "priority": "high"
}
```

#### 10. Delete Booking (Admin)
```http
DELETE /venue-bookings/:id
Authorization: Bearer <JWT_TOKEN>
```

#### 11. Get Upcoming Events (Admin)
```http
GET /venue-bookings/upcoming?days=30
Authorization: Bearer <JWT_TOKEN>
```

## 🔄 Booking Workflow

### 1. Customer Booking Flow
```
Customer selects venue → Fills simple booking form → Status: "new" → Admin notification
```

### 2. Admin Management Flow
```
Admin reviews booking → Updates status → Sets priority → Manages booking
```

### Status Lifecycle
- **new**: New booking request awaiting review
- **in-progress**: Admin is working on the booking
- **resolved**: Booking confirmed and resolved
- **closed**: Booking completed or cancelled

### Priority Levels
- **low**: Standard bookings
- **medium**: Regular priority (default)
- **high**: Important bookings requiring attention
- **urgent**: Critical bookings needing immediate action

## 🛡️ Validation & Security

### Venue Validation
- **Name**: Required, max 200 characters
- **Image**: Required S3 upload
- **Venue Type**: Must be from predefined enum
- **Location**: Required, max 500 characters
- **Capacity/Seats**: Numeric limits (1-15000/10000)

### Booking Validation
- **Full Name**: Required, max 100 characters
- **Email**: Valid email format required
- **Phone Number**: 10-digit Indian mobile number
- **Venue ID**: Valid ObjectId reference to venue
- **Venue Name**: Required, max 200 characters
- **Date of Plan**: Must be future date
- **Message**: Required, max 1000 characters

## 💡 Frontend Integration Examples

### Venue Listing
```javascript
// Get all venues
const getVenues = async (filters = {}) => {
  const queryParams = new URLSearchParams(filters);
  const response = await fetch(`/venues?${queryParams}`);
  return response.json();
};

// Get venue details
const getVenueDetails = async (venueId) => {
  const response = await fetch(`/venues/${venueId}`);
  return response.json();
};
```

### Booking Submission
```javascript
// Submit venue booking
const submitBooking = async (bookingData) => {
  try {
    const response = await fetch('/venue-bookings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        fullName: bookingData.fullName,
        email: bookingData.email,
        phoneNumber: bookingData.phoneNumber,
        venueId: bookingData.venueId,
        venueName: bookingData.venueName,
        dateOfPlan: bookingData.dateOfPlan,
        message: bookingData.message
      })
    });

    const result = await response.json();
    if (result.success) {
      alert('Booking request submitted successfully!');
    }
    return result;
  } catch (error) {
    console.error('Booking submission failed:', error);
  }
};
```

### Admin Dashboard
```javascript
// Get bookings with filters
const getBookings = async (filters = {}) => {
  const queryParams = new URLSearchParams(filters);
  const response = await fetch(`/venue-bookings?${queryParams}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};

// Update booking status
const updateBookingStatus = async (bookingId, status, priority) => {
  const response = await fetch(`/venue-bookings/${bookingId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ status, priority })
  });
  return response.json();
};
```

## 🚀 Best Practices

### For Venue Management
1. **Upload high-quality images** for better presentation
2. **Keep venue information updated** regularly
3. **Use descriptive locations** for better searchability
4. **Set appropriate capacity limits** based on actual venue size

### For Booking Management
1. **Respond to bookings quickly** (within 24 hours)
2. **Update status regularly** to track progress
3. **Set appropriate priorities** based on urgency
4. **Monitor upcoming events** for better planning
5. **Keep customer communication clear** and professional

### For Customers
1. **Provide accurate contact information** in booking forms
2. **Be specific about event requirements** in the message
3. **Plan events well in advance** for better availability
4. **Include all relevant details** in the message field

## 🔧 Configuration

### Image Upload (S3)
- **Folder**: `venues/`
- **File Size**: 5MB limit
- **Formats**: JPEG, PNG, WebP
- **Naming**: `venues/{timestamp}-{random}.{extension}`

### Database Indexes
```javascript
// Venue indexes
db.venues.createIndex({ venueType: 1, isActive: 1 });
db.venues.createIndex({ capacity: 1 });
db.venues.createIndex({ sortOrder: 1 });

// Booking indexes
db.venueforms.createIndex({ status: 1, isActive: 1 });
db.venueforms.createIndex({ dateOfPlan: 1 });
db.venueforms.createIndex({ venueId: 1 });
db.venueforms.createIndex({ email: 1 });
db.venueforms.createIndex({ phoneNumber: 1 });
db.venueforms.createIndex({ priority: 1, status: 1 });
```

This documentation provides complete coverage of both Venue and Venue Booking systems for easy website integration.
