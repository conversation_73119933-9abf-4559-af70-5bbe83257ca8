$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTU1ODgxLCJleHAiOjE3NTExNTk0ODF9.GPbgqGvLiAH_0CeNZnkMcomH47jaTMAssmL4Zmhiokw'
}

Write-Host "=== Testing All Fixed Endpoints ==="

# Test Review Creation (without image)
Write-Host "`n1. Testing Review Creation..."
$reviewData = @{
    name = "Test User"
    review = "Great service!"
    star = 5
    relationship = "client"
    eventType = "wedding"
} | ConvertTo-Json

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData
    Write-Host "SUCCESS: Review created! fixedId: $($result.data.fixedId)"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}

# Test Hero Section Creation (without image)
Write-Host "`n2. Testing Hero Section Creation..."
$heroData = @{
    title = "Test Hero"
    subtitle = "Test Subtitle"
    description = "Test Description"
    buttonText = "Click Me"
    buttonLink = "#test"
} | ConvertTo-<PERSON><PERSON>

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/hero-section' -Method POST -Headers $headers -ContentType 'application/json' -Body $heroData
    Write-Host "SUCCESS: Hero section created! fixedId: $($result.data.fixedId)"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}

# Test Team Creation (without image)
Write-Host "`n3. Testing Team Creation..."
$teamData = @{
    name = "Test Team Member"
} | ConvertTo-Json

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/team' -Method POST -Headers $headers -ContentType 'application/json' -Body $teamData
    Write-Host "SUCCESS: Team member created! fixedId: $($result.data.fixedId)"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}

# Test GET endpoints
Write-Host "`n4. Testing GET Endpoints..."
$endpoints = @(
    'http://localhost:8005/api/gallery',
    'http://localhost:8005/api/reviews', 
    'http://localhost:8005/api/hero-section',
    'http://localhost:8005/api/team',
    'http://localhost:8005/api/venues',
    'http://localhost:8005/api/venue-bookings',
    'http://localhost:8005/api/services'
)

foreach ($endpoint in $endpoints) {
    $name = ($endpoint -split '/')[-1]
    try {
        $result = Invoke-RestMethod -Uri $endpoint -Headers $headers
        Write-Host "✅ $name - SUCCESS"
    } catch {
        Write-Host "❌ $name - ERROR: $($_.Exception.Message)"
    }
}

Write-Host "`n=== Test Complete ==="
