<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service PNG Icons Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .service-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 50%;
            padding: 15px;
            border: 2px solid #e9ecef;
        }

        .service-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .service-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .icon-status {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .icon-fallback {
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .loading {
            text-align: center;
            padding: 3rem;
        }

        .error {
            color: #dc3545;
            text-align: center;
            padding: 2rem;
        }

        .icon-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🖼️ Service PNG Icons Test</h1>
                <p class="text-center text-muted mb-4">
                    Testing PNG/JPG icons - No more SVG issues!
                </p>
            </div>
        </div>

        <div class="icon-info">
            <h5>✅ PNG Icon System Features:</h5>
            <ul class="mb-0">
                <li><strong>File Types:</strong> PNG, JPG, GIF, WebP (NO MORE SVG)</li>
                <li><strong>Storage:</strong> <code>services/icons/</code> folder in S3</li>
                <li><strong>Field:</strong> <code>icons</code> (required)</li>
                <li><strong>Benefits:</strong> Always displays properly, no browser issues</li>
                <li><strong>Size Limit:</strong> 10MB maximum</li>
            </ul>
        </div>

        <div id="loading" class="loading">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading services...</span>
            </div>
            <p class="mt-2">Loading services with PNG icons...</p>
        </div>

        <div id="error" class="error d-none">
            <div class="alert alert-danger">
                <h5>❌ Error Loading Services</h5>
                <p id="error-message">Failed to load services</p>
            </div>
        </div>

        <div id="services-container" class="row d-none">
            <!-- Services will be loaded here -->
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">📋 PNG Icon System Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>✅ What Changed:</h6>
                                <ul>
                                    <li>Replaced SVG icons with PNG/JPG</li>
                                    <li>Simplified to single icon field</li>
                                    <li>Removed complex fallback system</li>
                                    <li>Icons field is now required</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>🎯 Benefits:</h6>
                                <ul>
                                    <li>Always displays properly</li>
                                    <li>No browser compatibility issues</li>
                                    <li>No download problems</li>
                                    <li>Simple and reliable</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API base URL
        const API_BASE = '';

        // Fetch services from API
        async function fetchServices() {
            try {
                const response = await fetch(`${API_BASE}/services?limit=20&sortBy=sortOrder&sortOrder=asc`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || 'Failed to fetch services');
                }
                
                return result.data.services;
            } catch (error) {
                console.error('Error fetching services:', error);
                throw error;
            }
        }

        // Test PNG icon loading
        function testPNGIconLoad(iconUrl, serviceTitle) {
            return new Promise((resolve) => {
                if (!iconUrl) {
                    resolve({
                        success: false,
                        iconType: 'none',
                        displayIcon: null,
                        fallbackInitial: serviceTitle.charAt(0).toUpperCase()
                    });
                    return;
                }

                const img = new Image();
                
                img.onload = () => {
                    resolve({
                        success: true,
                        iconType: 'png/jpg',
                        displayIcon: iconUrl,
                        fallbackInitial: null
                    });
                };
                
                img.onerror = () => {
                    resolve({
                        success: false,
                        iconType: 'failed',
                        displayIcon: null,
                        fallbackInitial: serviceTitle.charAt(0).toUpperCase()
                    });
                };
                
                img.src = iconUrl;
            });
        }

        // Display services with PNG icon testing
        async function displayServices(services) {
            const container = document.getElementById('services-container');
            
            if (!services || services.length === 0) {
                container.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <h5>📝 No Services Found</h5>
                            <p>No services are currently available. Please add some services first.</p>
                        </div>
                    </div>
                `;
                return;
            }

            // Test each service icon
            const serviceCards = await Promise.all(services.map(async (service) => {
                const iconTest = await testPNGIconLoad(service.icons, service.title);
                
                let iconHtml;
                let statusHtml;
                
                if (iconTest.success) {
                    iconHtml = `<img src="${iconTest.displayIcon}" alt="${service.title} Icon">`;
                    statusHtml = `<div class="icon-status text-success">✅ PNG icon loaded successfully</div>`;
                } else {
                    iconHtml = `<div class="icon-fallback">${iconTest.fallbackInitial}</div>`;
                    statusHtml = `<div class="icon-status text-danger">❌ Icon failed, showing fallback</div>`;
                }

                return `
                    <div class="col-md-4 col-sm-6 mb-4">
                        <div class="card service-card h-100">
                            <div class="card-body text-center">
                                <div class="service-icon mb-3">
                                    ${iconHtml}
                                </div>
                                ${statusHtml}
                                <h5 class="card-title">${service.title}</h5>
                                <p class="card-text">${service.description}</p>
                                <div class="mt-auto">
                                    <small class="text-muted">
                                        Icon URL: ${service.icons ? '✅ Available' : '❌ Missing'}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }));

            container.innerHTML = serviceCards.join('');
        }

        // Show error message
        function showError(message) {
            document.getElementById('loading').classList.add('d-none');
            document.getElementById('error').classList.remove('d-none');
            document.getElementById('error-message').textContent = message;
        }

        // Show services
        function showServices(services) {
            document.getElementById('loading').classList.add('d-none');
            document.getElementById('services-container').classList.remove('d-none');
            displayServices(services);
        }

        // Load services on page load
        async function loadServices() {
            try {
                console.log('🔄 Loading services with PNG icon testing...');
                const services = await fetchServices();
                console.log(`✅ Loaded ${services.length} services`);
                
                // Log icon information for debugging
                services.forEach(service => {
                    console.log(`Service: ${service.title}`);
                    console.log(`  - PNG Icon: ${service.icons || 'None'}`);
                });
                
                showServices(services);
            } catch (error) {
                console.error('❌ Error loading services:', error);
                showError(error.message);
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', loadServices);
    </script>
</body>
</html>
