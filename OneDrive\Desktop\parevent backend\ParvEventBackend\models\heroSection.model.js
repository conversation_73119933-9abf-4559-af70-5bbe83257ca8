const mongoose = require('mongoose');

const heroSectionSchema = new mongoose.Schema({
    title: {
        type: String,
        required: [true, 'Title is required'],
        trim: true,
        maxlength: [200, 'Title cannot exceed 200 characters']
    },
    subtitle: {
        type: String,
        trim: true,
        maxlength: [300, 'Subtitle cannot exceed 300 characters']
    },
    description: {
        type: String,
        trim: true,
        maxlength: [500, 'Description cannot exceed 500 characters']
    },
    image: {
        type: String,
        required: [true, 'Image is required']
    },
    buttonText: {
        type: String,
        trim: true,
        maxlength: [50, 'Button text cannot exceed 50 characters'],
        default: 'Learn More'
    },
    buttonLink: {
        type: String,
        trim: true,
        default: '#'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isPrimary: {
        type: Boolean,
        default: false
    },

    sortOrder: {
        type: Number,
        default: 0
    },
    displayDuration: {
        type: Number,
        default: 5000, // 5 seconds in milliseconds
        min: [1000, 'Display duration must be at least 1 second'],
        max: [30000, 'Display duration cannot exceed 30 seconds']
    }
}, {
    timestamps: true
});

// Index for better performance
heroSectionSchema.index({ isActive: 1, isPrimary: 1 });

heroSectionSchema.index({ sortOrder: 1 });



// Pre-save middleware to ensure only one primary hero section
heroSectionSchema.pre('save', async function(next) {
    if (this.isPrimary && this.isActive) {
        // Set all other hero sections to not primary
        await this.constructor.updateMany(
            { _id: { $ne: this._id }, isPrimary: true },
            { isPrimary: false }
        );
    }
    next();
});

// Method to get public data
heroSectionSchema.methods.getPublicData = function() {
    return {
        _id: this._id,
        title: this.title,
        subtitle: this.subtitle,
        description: this.description,
        image: this.image,
        buttonText: this.buttonText,
        buttonLink: this.buttonLink,
        isPrimary: this.isPrimary,

        sortOrder: this.sortOrder,
        displayDuration: this.displayDuration,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt
    };
};



// Static method to get primary hero section
heroSectionSchema.statics.getPrimaryHero = function() {
    return this.findOne({ isActive: true, isPrimary: true });
};

// Static method to get all active hero sections
heroSectionSchema.statics.getActiveHeros = function() {
    return this.find({ isActive: true }).sort({ sortOrder: 1, createdAt: -1 });
};

module.exports = mongoose.model('HeroSection', heroSectionSchema);
