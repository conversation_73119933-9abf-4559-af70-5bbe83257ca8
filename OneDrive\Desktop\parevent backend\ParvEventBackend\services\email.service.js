require("dotenv").config();
const nodemailer = require("nodemailer");

// Updated contact form email function for new schema
const sendContactFormEmail = async (contactData) => {
  const { name, email, countryCode, phoneNumber, service, message } = contactData;
  const fullPhone = `${countryCode}${phoneNumber}`;

  try {
    // Create a transporter object
    const transporter = nodemailer.createTransport({
      service: "gmail",
      host: "smtp.gmail.com",
      port: 465,
      secure: true,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Beautiful HTML email for the user (Congratulations)
    const userMailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: "🎉 Thank You for Reaching Out to AGKRAFT!",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .highlight { background: #e8f4fd; padding: 15px; border-left: 4px solid #2196F3; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; }
            .button { background: #4CAF50; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 Congratulations!</h1>
              <p>Your message has been successfully submitted</p>
            </div>
            <div class="content">
              <h2>Dear ${name},</h2>
              <p>Thank you for contacting <strong>AGKRAFT Software Company</strong>! We have received your message and our expert team will review it carefully.</p>

              <div class="highlight">
                <h3>📋 Your Submission Details:</h3>
                <p><strong>Name:</strong> ${name}</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Phone:</strong> ${fullPhone}</p>
                <p><strong>Service Interest:</strong> ${service}</p>
                <p><strong>Message:</strong> ${message}</p>
              </div>

              <p>🚀 <strong>What happens next?</strong></p>
              <ul>
                <li>Our team will review your requirements within 24 hours</li>
                <li>We'll contact you via phone or email to discuss your project</li>
                <li>Get a personalized quote tailored to your needs</li>
              </ul>

              <a href="https://agkraft.com" class="button">Visit Our Website</a>

              <div class="footer">
                <p><strong>AGKRAFT Team</strong><br>
                Email: <EMAIL><br>
                Phone: +91-8541953971<br>
                Website: www.agkraft.com</p>
              </div>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    // Admin notification email with full details
    const adminMailOptions = {
      from: process.env.EMAIL_USER,
      to: "<EMAIL>", // Your email
      subject: "🔔 New Contact Form Submission - AGKRAFT",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #ff6b35; color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .detail-box { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .urgent { background: #ffebee; border-left: 4px solid #f44336; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🔔 New Contact Submission</h1>
              <p>Someone wants to connect with AGKRAFT!</p>
            </div>
            <div class="content">
              <div class="detail-box urgent">
                <h2>📞 Contact Details</h2>
                <p><strong>Name:</strong> ${name}</p>
                <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
                <p><strong>Phone:</strong> <a href="tel:${fullPhone}">${fullPhone}</a></p>
                <p><strong>Service Interest:</strong> ${service}</p>
              </div>

              <div class="detail-box">
                <h3>💬 Message from Customer:</h3>
                <p style="background: #f5f5f5; padding: 15px; border-radius: 5px; font-style: italic;">"${message}"</p>
              </div>

              <div class="detail-box">
                <h3>⏰ Submission Time:</h3>
                <p>${new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}</p>
              </div>

              <p style="text-align: center; margin-top: 30px;">
                <strong>Action Required:</strong> Please contact the customer within 24 hours for best response rate.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    // Send emails to both user and admin
    await transporter.sendMail(userMailOptions);
    console.log("✅ Congratulations email sent to user successfully!");

    await transporter.sendMail(adminMailOptions);
    console.log("✅ Notification email sent to admin successfully!");

    return { success: true, message: "Emails sent successfully" };

  } catch (error) {
    console.error("❌ Error sending emails:", error);
    throw new Error("Failed to send emails");
  }
};

// Legacy function for old contact form (keeping for backward compatibility)
const sendEmail = async (formData) => {
  const { first_name, last_name, email, phone, country, city, message } = formData;

  try {
    // Create a transporter object only once for efficiency
    const transporter = nodemailer.createTransporter({
      service: "gmail",
      host: "smtp.gmail.com",
      port: 465,
      secure: true,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Email content for the user
    const userMailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Thank You for Reaching Out to AGKRAFT!",
      text: `
        Dear ${first_name} ${last_name},

        Thank you for contacting AGKRAFT Software Company! We have received your message, and our team will review it and respond as soon as possible.

        Here is a summary of the information you provided:
        - **Name:** ${first_name} ${last_name}
        - **Email:** ${email}
        - **Phone:** ${phone}
        - **Country:** ${country}
        - **City:** ${city}
        - **Message:** ${message}

        We appreciate your interest in our services and will strive to get back to you within the next 24-48 hours. In the meantime, feel free to explore our website for more information about our offerings and recent projects.

        Best regards,

        AGKRAFT Team
        Email: <EMAIL>
        Website: www.agkraft.com
        Phone: +91-8541953971
      `,
    };

    // Email content for you (to notify about the new form submission)
    const adminMailOptions = {
      from: process.env.EMAIL_USER,
      to: process.env.ADMIN_EMAIL, // Your email address
      subject: "New Contact Form Submission on AGKRAFT",
      text: `
        You have a new contact form submission from AGKRAFT:

        - **Name:** ${first_name} ${last_name}
        - **Email:** ${email}
        - **Phone:** ${phone}
        - **Country:** ${country}
        - **City:** ${city}
        - **Message:** ${message}

        Please review and respond as necessary.
      `,
    };

    // Send emails to both the user and you
    await transporter.sendMail(userMailOptions);
    console.log("Email sent to user successfully!");

    await transporter.sendMail(adminMailOptions);
    console.log("Notification email sent to admin successfully!");

  } catch (error) {
    console.error("Error sending email:", error);
    throw new Error("Failed to send email");
  }
};

// Generic email sending function for authentication
const sendGenericEmail = async (to, subject, html) => {
  try {
    // Create a transporter object
    const transporter = nodemailer.createTransport({
      service: "gmail",
      host: process.env.EMAIL_HOST || "smtp.gmail.com",
      port: process.env.EMAIL_PORT || 587,
      secure: false, // Use TLS
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Email options
    const mailOptions = {
      from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
      to: to,
      subject: subject,
      html: html,
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);
    console.log("Email sent successfully to:", to);
    return result;

  } catch (error) {
    console.error("Error sending email:", error);
    throw new Error("Failed to send email");
  }
};

module.exports = {
  sendContactEmail: sendEmail, // Original contact form email function (legacy)
  sendContactFormEmail, // New contact form email function
  sendEmail: sendGenericEmail // Generic email function for auth service
};
