{"name": "agkraftbackend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "axios": "^1.10.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cookie": "^0.7.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "express": "^4.21.0", "form-data": "^4.0.3", "http-status-codes": "^2.3.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mkdirp": "^3.0.1", "mongoose": "^8.7.0", "mongoose-sequence": "^6.0.1", "multer": "^2.0.1", "multer-s3": "^3.0.1", "nodemailer": "^6.9.15", "nodemon": "^3.1.7", "qrcode": "^1.5.4", "rimraf": "^6.0.1", "speakeasy": "^2.0.0", "twilio": "^5.3.3", "validator": "^13.12.0"}, "description": ""}