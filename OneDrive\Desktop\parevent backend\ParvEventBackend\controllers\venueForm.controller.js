const VenueForm = require('../models/venueForm.model');
const Venue = require('../models/venue.model');
const { StatusCodes } = require('http-status-codes');
const Joi = require('joi');

// Validation schemas
const venueFormSchema = Joi.object({
    fullName: Joi.string()
        .required()
        .max(100)
        .messages({
            'string.max': 'Full name cannot exceed 100 characters',
            'any.required': 'Full name is required'
        }),
    email: Joi.string()
        .email()
        .required()
        .messages({
            'string.email': 'Please enter a valid email',
            'any.required': 'Email is required'
        }),
    phoneNumber: Joi.string()
        .pattern(/^[6-9]\d{9}$/)
        .required()
        .messages({
            'string.pattern.base': 'Please enter a valid 10-digit phone number',
            'any.required': 'Phone number is required'
        }),
    venueId: Joi.string()
        .required()
        .messages({
            'any.required': 'Venue selection is required'
        }),
    venueName: Joi.string()
        .required()
        .max(200)
        .messages({
            'string.max': 'Venue name cannot exceed 200 characters',
            'any.required': 'Venue name is required'
        }),
    dateOfPlan: Joi.date()
        .greater('now')
        .required()
        .messages({
            'date.greater': 'Date of plan must be in the future',
            'any.required': 'Date of plan is required'
        }),
    message: Joi.string()
        .required()
        .max(1000)
        .messages({
            'string.max': 'Message cannot exceed 1000 characters',
            'any.required': 'Message is required'
        })
});

const updateVenueFormSchema = Joi.object({
    status: Joi.string()
        .valid('new', 'in-progress', 'resolved', 'closed'),
    priority: Joi.string()
        .valid('low', 'medium', 'high', 'urgent')
});

// Create venue booking form
const createVenueForm = async (req, res) => {
    try {
        console.log('📝 Creating venue booking form:', req.body);
        
        // Validate request body
        const { error, value } = venueFormSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Verify venue exists
        const venue = await Venue.findOne({
            _id: value.venueId,
            isActive: true
        });

        if (!venue) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Selected venue not found or not available'
            });
        }

        // Create venue form with simplified data
        const venueFormData = {
            fullName: value.fullName,
            email: value.email,
            phoneNumber: value.phoneNumber,
            venueId: venue._id,
            venueName: value.venueName,
            dateOfPlan: value.dateOfPlan,
            message: value.message
        };

        const venueForm = new VenueForm(venueFormData);
        await venueForm.save();

        console.log('✅ Venue booking form created:', venueForm._id);

        res.status(StatusCodes.CREATED).json({
            success: true,
            message: 'Venue booking request submitted successfully',
            data: venueForm.getPublicData()
        });

    } catch (error) {
        console.error('❌ Create venue form error:', error);
        
        if (error.code === 11000) {
            return res.status(StatusCodes.CONFLICT).json({
                success: false,
                message: 'Booking already exists'
            });
        }

        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to submit venue booking request',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get all venue forms (Admin only)
const getAllVenueForms = async (req, res) => {
    try {
        console.log('📋 Fetching all venue booking forms');
        
        const {
            page = 1,
            limit = 10,
            status,
            priority,
            venueId,
            startDate,
            endDate,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;

        // Build filter
        const filter = { isActive: true };

        if (status && status !== 'all') {
            filter.status = status;
        }

        if (priority && priority !== 'all') {
            filter.priority = priority;
        }

        if (venueId) {
            filter.venueId = venueId;
        }

        if (startDate || endDate) {
            filter.dateOfPlan = {};
            if (startDate) filter.dateOfPlan.$gte = new Date(startDate);
            if (endDate) filter.dateOfPlan.$lte = new Date(endDate);
        }

        // Build sort
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query with pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        
        const [venueForms, total] = await Promise.all([
            VenueForm.find(filter)
                .populate('venueId', 'title venueType location')
                .sort(sort)
                .skip(skip)
                .limit(parseInt(limit)),
            VenueForm.countDocuments(filter)
        ]);

        const totalPages = Math.ceil(total / parseInt(limit));

        console.log(`✅ Found ${venueForms.length} venue booking forms`);

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                bookings: venueForms.map(form => form.getAdminData()),
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalItems: total,
                    itemsPerPage: parseInt(limit),
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1
                }
            }
        });

    } catch (error) {
        console.error('❌ Get all venue forms error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch venue booking forms',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get venue form by ID
const getVenueFormById = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🔍 Fetching venue form by ID:', id);

        // Find by MongoDB ObjectId only
        let venueForm = await VenueForm.findOne({
            _id: id,
            isActive: true
        }).populate('venueId', 'name venueType location capacity seats');

        if (!venueForm) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Venue booking form not found'
            });
        }

        console.log('✅ Venue form found:', venueForm._id);

        res.status(StatusCodes.OK).json({
            success: true,
            data: venueForm.getAdminData()
        });

    } catch (error) {
        console.error('❌ Get venue form by ID error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch venue booking form',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Update venue form (Admin only)
const updateVenueForm = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('📝 Updating venue form:', id);

        // Validate request body
        const { error, value } = updateVenueFormSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Find venue form
        let venueForm = await VenueForm.findOne({
            _id: id,
            isActive: true
        });

        if (!venueForm) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Venue booking form not found'
            });
        }

        // Update fields
        Object.keys(value).forEach(key => {
            venueForm[key] = value[key];
        });

        await venueForm.save();

        console.log('✅ Venue form updated:', venueForm._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Venue booking form updated successfully',
            data: venueForm.getAdminData()
        });

    } catch (error) {
        console.error('❌ Update venue form error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to update venue booking form',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Delete venue form (Admin only)
const deleteVenueForm = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🗑️ Deleting venue form:', id);

        // Find and soft delete venue form
        const venueForm = await VenueForm.findOneAndUpdate(
            {
                _id: id,
                isActive: true
            },
            { isActive: false },
            { new: true }
        );

        if (!venueForm) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Venue booking form not found'
            });
        }

        console.log('✅ Venue form deleted:', venueForm._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Venue booking form deleted successfully'
        });

    } catch (error) {
        console.error('❌ Delete venue form error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to delete venue booking form',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get upcoming events (Admin only)
const getUpcomingEvents = async (req, res) => {
    try {
        const { days = 30 } = req.query;
        console.log(`📅 Fetching upcoming events for next ${days} days`);

        const upcomingEvents = await VenueForm.getUpcomingEvents(parseInt(days));

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                events: upcomingEvents.map(event => event.getAdminData()),
                count: upcomingEvents.length
            }
        });

    } catch (error) {
        console.error('❌ Get upcoming events error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch upcoming events',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

module.exports = {
    createVenueForm,
    getAllVenueForms,
    getVenueFormById,
    updateVenueForm,
    deleteVenueForm,
    getUpcomingEvents
};
