# 🏛️ Venues API Documentation (Simplified)

## Overview
Simplified API documentation for Venues management in ParvEvent Backend. Simple venue system with only essential fields.

## Base URL
```
http://localhost:8005/api/venues
```

## Authentication
All endpoints require admin authentication:
```
Authorization: Bearer <JWT_TOKEN>
```

---

## 📋 **API Endpoints**

### 1. Create Venue
**POST** `/api/venues`

Creates a new venue with single image upload.

**Content-Type:** `multipart/form-data`

**Form Fields:**
```javascript
{
  "name": "Grand Palace Banquet Hall",          // Required, max 200 chars
  "venueType": "banquet-hall",                  // Required, predefined values
  "location": "123 Main Street, Mumbai, Maharashtra", // Required, max 500 chars (manual entry)
  "capacity": 750,                              // Required, 1-15000 (total capacity)
  "seats": 500,                                 // Required, 1-10000 (seating capacity)
  "sortOrder": 1                                // Optional, default 0
}

// File upload (required):
image: File                    // Single venue image (required)
```

**Venue Types:**
- banquet-hall, outdoor, resort, hotel, farmhouse
- palace, garden, beach, other

**Response:**
```javascript
{
  "success": true,
  "message": "Venue created successfully",
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "Grand Palace Banquet Hall",
    "image": "https://s3.amazonaws.com/bucket/venues/main.jpg",
    "venueType": "banquet-hall",
    "location": "123 Main Street, Mumbai, Maharashtra",
    "capacity": 750,
    "seats": 500,
    "sortOrder": 1,
    "isActive": true,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

### 2. Get All Venues
**GET** `/api/venues`

Retrieves all active venues with pagination and filtering.

**Query Parameters:**
```
?page=1                    // Page number (default: 1)
&limit=10                  // Items per page (default: 10, max: 100)
&sortBy=createdAt          // Sort field (default: createdAt)
&sortOrder=desc            // Sort order: asc/desc (default: desc)
&venueType=banquet-hall    // Filter by venue type
&search=palace             // Search in name and location
```

**Response:**
```javascript
{
  "success": true,
  "data": {
    "venues": [
      {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
        "name": "Grand Palace Banquet Hall",
        "image": "https://s3.amazonaws.com/bucket/venues/main.jpg",
        "venueType": "banquet-hall",
        "location": "123 Main Street, Mumbai, Maharashtra",
        "capacity": 750,
        "seats": 500,
        "sortOrder": 1,
        "isActive": true,
        "createdAt": "2023-12-01T10:00:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 8,
      "totalItems": 75,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

### 3. Get Venue by ID
**GET** `/api/venues/:id`

Retrieves a specific venue by MongoDB ObjectId and increments view count.

**Parameters:**
- `id` - MongoDB ObjectId

**Response:**
```javascript
{
  "success": true,
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "Grand Palace Banquet Hall",
    "image": "https://s3.amazonaws.com/bucket/venues/main.jpg",
    "venueType": "banquet-hall",
    "location": "123 Main Street, Mumbai, Maharashtra",
    "capacity": 750,
    "seats": 500,
    "sortOrder": 1,
    "isActive": true,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

### 4. Update Venue
**PUT** `/api/venues/:id`

Updates an existing venue. Supports both JSON and multipart/form-data.

**Content-Type:** `multipart/form-data` or `application/json`

**Parameters:**
- `id` - MongoDB ObjectId

**Form Fields:** (All optional)
```javascript
{
  "name": "Updated Grand Palace",
  "venueType": "resort",
  "location": "Updated address, Mumbai, Maharashtra",
  "capacity": 800,
  "seats": 600,
  "sortOrder": 2
}

// File upload (optional):
image: File                    // New venue image
```

**Response:**
```javascript
{
  "success": true,
  "message": "Venue updated successfully",
  "data": {
    // Updated venue object
  }
}
```

### 5. Delete Venue
**DELETE** `/api/venues/:id`

Soft deletes a venue (sets isActive: false).

**Parameters:**
- `id` - MongoDB ObjectId

**Response:**
```javascript
{
  "success": true,
  "message": "Venue deleted successfully",
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "Grand Palace Banquet Hall",
    "isActive": false
  }
}
```

---

## 🚨 **Error Responses**

### Validation Error (400)
```javascript
{
  "success": false,
  "message": "Validation error",
  "errors": [
    "Name is required",
    "Capacity must be at least 1",
    "Seats must be at least 1"
  ]
}
```

### Not Found (404)
```javascript
{
  "success": false,
  "message": "Venue not found"
}
```

### Server Error (500)
```javascript
{
  "success": false,
  "message": "Failed to create venue",
  "debug": "Detailed error message (development only)"
}
```

---

## 📝 **Usage Examples**

### Frontend Integration
```javascript
// Create venue with single image
const formData = new FormData();
formData.append('name', 'Grand Palace Banquet Hall');
formData.append('venueType', 'banquet-hall');
formData.append('location', '123 Main Street, Mumbai, Maharashtra');
formData.append('capacity', '750');
formData.append('seats', '500');
formData.append('sortOrder', '1');
formData.append('image', venueImageFile);

const response = await fetch('/api/venues', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Get venues with filtering
const venues = await fetch('/api/venues?venueType=banquet-hall&search=palace&page=1&limit=10', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### Admin Features
- ✅ Create unlimited venues for different locations
- ✅ Single image upload per venue
- ✅ Simple manual location entry
- ✅ Basic capacity and seating information
- ✅ Venue type categorization
- ✅ Simple filtering and search
- ✅ Custom sorting order

---

## 🔧 **Technical Notes**

- **File Upload**: Uses AWS S3 for image storage
- **Image Types**: Supports JPG, PNG, GIF, WebP
- **File Size**: Maximum 5MB per image
- **Single Image**: One image per venue
- **Database**: MongoDB with ObjectId
- **Validation**: Joi schema validation
- **Security**: JWT authentication required
- **Soft Delete**: Venues are never permanently deleted
- **Manual Location**: Simple text-based location entry
- **Search**: Full-text search in name and location

---

*Last Updated: December 2024*
