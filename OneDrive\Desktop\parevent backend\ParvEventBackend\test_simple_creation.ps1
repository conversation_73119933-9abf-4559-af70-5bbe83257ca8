$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTYyNDI5LCJleHAiOjE3NTExNjYwMjl9.QEYGEFai2070jIVyqtSL26e40KGUNNyropKf9nR-U_c'
}

Write-Host "Testing Simple Creation After fixedId Removal"
Write-Host "=============================================="

# Test 1: Create Review
Write-Host "`nTest 1: Creating Review..."
$reviewData = @{
    name = "Test User After Fix"
    review = "Testing after removing all fixedId references"
    star = 5
    relationship = "client"
    eventType = "wedding"
} | ConvertTo-Json

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData
    Write-Host "SUCCESS: Review created - ID: $($result.data._id)"
} catch {
    Write-Host "ERROR: Review failed - $($_.Exception.Message)"
}

# Test 2: Create Another Review (should not give duplicate error)
Write-Host "`nTest 2: Creating Second Review..."
$reviewData2 = @{
    name = "Another Test User"
    review = "Second review to test no duplicate errors"
    star = 4
    relationship = "client"
    eventType = "corporate"
} | ConvertTo-Json

try {
    $result2 = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData2
    Write-Host "SUCCESS: Second review created - ID: $($result2.data._id)"
} catch {
    Write-Host "ERROR: Second review failed - $($_.Exception.Message)"
}

Write-Host "`nTest Complete!"
