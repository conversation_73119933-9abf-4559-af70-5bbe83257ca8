# 🚀 ParvEvent Backend - COMPLETE API DOCUMENTATION

## 📋 Base Information
- **Base URL**: `http://localhost:8005/api`
- **Server Status**: ✅ Running on port 8005
- **Database**: ✅ MongoDB Atlas connected
- **Storage**: ✅ AWS S3 integrated
- **CORS**: ✅ Configured for frontend

---

## 🔐 Authentication System

### Auth Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/auth/signup` | Admin registration | ❌ |
| POST | `/auth/test-signup` | Test signup (no DB) | ❌ |
| POST | `/auth/verify-email` | Email verification | ❌ |
| POST | `/auth/verify-2fa` | 2FA verification | ❌ |
| POST | `/auth/login` | Admin login | ❌ |
| POST | `/auth/login-simple` | Simple login (testing) | ❌ |
| POST | `/auth/login-direct` | Direct login | ❌ |
| POST | `/auth/login-email` | Email-based login | ❌ |
| POST | `/auth/login-quick` | Quick login by ID | ❌ |
| POST | `/auth/logout` | Admin logout | ✅ |
| GET | `/auth/profile` | Get admin profile | ✅ |
| POST | `/auth/resend-otp` | Resend email OTP | ❌ |

### Sample Auth Request:
```json
POST /api/auth/signup
{
  "firstname": "John",
  "lastname": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmpassword": "password123"
}
```

---

## 🖼️ Gallery Management

### Gallery Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/gallery` | Get all gallery items | ❌ |
| GET | `/gallery/:id` | Get gallery by ID/fixedId | ❌ |
| POST | `/gallery` | Create gallery item | ✅ |
| PUT | `/gallery/:id` | Update gallery item | ✅ |
| DELETE | `/gallery/:id` | Delete gallery item | ✅ |

### Gallery Features:
- **Image Upload**: S3 `gallery/` folder
- **Keywords**: Array support for tagging
- **Categories**: wedding, corporate, birthday, anniversary, other
- **Fixed ObjectID**: `gallery_1703123456789_abc123def`
- **Pagination**: page, limit, sortBy, sortOrder
- **Filtering**: category, keyword search

### Sample Gallery Request:
```json
POST /api/gallery (multipart/form-data)
{
  "title": "Wedding Gallery",
  "description": "Beautiful wedding moments",
  "keywords": ["wedding", "photography", "celebration"],
  "category": "wedding",
  "sortOrder": 1,
  "image": [FILE]
}
```

---

## ⭐ Review Management

### Review Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/reviews` | Get all reviews | ❌ |
| GET | `/reviews/stats` | Get review statistics | ❌ |
| GET | `/reviews/:id` | Get review by ID/fixedId | ❌ |
| POST | `/reviews` | Create review | ✅ |
| PUT | `/reviews/:id` | Update review | ✅ |
| DELETE | `/reviews/:id` | Delete review | ✅ |

### Review Features:
- **Image Upload**: S3 `reviews/` folder
- **Star Rating**: 1-5 with validation
- **Relationships**: bride, groom, parent, friend, relative, colleague, client, other
- **Event Types**: wedding, corporate, birthday, anniversary, other
- **Statistics**: Average rating, distribution
- **Fixed ObjectID**: `review_1703123456789_xyz789ghi`

### Sample Review Request:
```json
POST /api/reviews (multipart/form-data)
{
  "name": "John Doe",
  "relationship": "groom",
  "review": "Amazing service! Highly recommended.",
  "star": 5,
  "eventType": "wedding",
  "isFeatured": false,
  "image": [FILE]
}
```

---

## 🎯 Hero Section Management

### Hero Section Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/hero-section` | Get all hero sections | ❌ |
| GET | `/hero-section/primary` | Get primary hero section | ❌ |
| GET | `/hero-section/:id` | Get hero section by ID/fixedId | ❌ |
| POST | `/hero-section` | Create hero section | ✅ |
| PUT | `/hero-section/:id` | Update hero section | ✅ |
| DELETE | `/hero-section/:id` | Delete hero section | ✅ |

### Hero Section Features:
- **Image Upload**: S3 `hero-section/` folder
- **Primary Management**: Only one primary hero
- **Display Duration**: Configurable timing
- **Button Customization**: Text and link
- **Fixed ObjectID**: `hero_1703123456789_mno456pqr`

### Sample Hero Section Request:
```json
POST /api/hero-section (multipart/form-data)
{
  "title": "Welcome to ParvEvent",
  "subtitle": "Creating Memorable Moments",
  "description": "We specialize in making your special day unforgettable",
  "buttonText": "Get Started",
  "buttonLink": "/contact",
  "isPrimary": true,
  "displayDuration": 5000,
  "image": [FILE]
}
```

---

## 🏢 Venue Management

### Venue Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/venues` | Get all venues | ❌ |
| GET | `/venues/:id` | Get venue by ID/fixedId | ❌ |
| POST | `/venues` | Create venue | ✅ |
| PUT | `/venues/:id` | Update venue | ✅ |
| DELETE | `/venues/:id` | Delete venue | ✅ |

### Venue Features:
- **Multiple Images**: Main + gallery images to S3 `venues/` folder
- **Location**: Address with coordinates
- **Pricing**: Base price + additional charges
- **Capacity**: Total seats and capacity
- **Amenities**: Array of facilities
- **Venue Types**: banquet-hall, outdoor, resort, hotel, farmhouse, palace, garden, beach, other
- **Fixed ObjectID**: `venue_1703123456789_stu123vwx`

### Sample Venue Request:
```json
POST /api/venues (multipart/form-data)
{
  "title": "Grand Ballroom",
  "description": "Elegant venue for weddings",
  "location": {
    "address": "123 Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400001",
    "coordinates": {
      "latitude": 19.0760,
      "longitude": 72.8777
    }
  },
  "venueType": "banquet-hall",
  "totalSeats": 200,
  "capacityTotal": 300,
  "price": {
    "basePrice": 50000,
    "currency": "INR",
    "priceType": "per-day",
    "additionalCharges": {
      "decoration": 10000,
      "catering": 500,
      "parking": 2000,
      "security": 5000
    }
  },
  "amenities": ["parking", "ac", "wifi", "catering"],
  "image": [FILE],
  "images": [FILES]
}
```

---

## 📝 Venue Booking Management

### Venue Booking Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/venue-bookings` | Submit booking (Public) | ❌ |
| GET | `/venue-bookings` | Get all bookings | ✅ |
| GET | `/venue-bookings/upcoming` | Get upcoming events | ✅ |
| GET | `/venue-bookings/:id` | Get booking by ID/fixedId | ✅ |
| PUT | `/venue-bookings/:id` | Update booking | ✅ |
| DELETE | `/venue-bookings/:id` | Delete booking | ✅ |

### Venue Booking Features:
- **Public Submission**: No auth required for booking
- **Venue Validation**: Checks availability and capacity
- **Status Tracking**: pending, confirmed, cancelled, completed
- **Priority System**: low, medium, high, urgent
- **Admin Management**: Notes, follow-up dates, cost estimation
- **Fixed ObjectID**: `booking_1703123456789_abc123def`

### Sample Venue Booking Request:
```json
POST /api/venue-bookings
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "9876543210",
  "address": {
    "street": "456 Oak Avenue",
    "city": "Delhi",
    "state": "Delhi",
    "pincode": "110001"
  },
  "eventDate": "2024-06-15T00:00:00.000Z",
  "eventType": "wedding",
  "venueId": "venue_1703123456789_stu123vwx",
  "venueName": "Grand Ballroom",
  "guestCount": 250,
  "budget": {
    "min": 100000,
    "max": 200000,
    "currency": "INR"
  },
  "requirements": {
    "catering": true,
    "decoration": true,
    "photography": false,
    "music": true,
    "parking": true
  },
  "specialRequests": "Need vegetarian catering only"
}
```

---

## 🛠️ Services Management

### Services Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/services` | Get all services | ❌ |
| GET | `/services/featured` | Get featured services | ❌ |
| GET | `/services/statistics` | Get service statistics | ❌ |
| GET | `/services/category/:category` | Get services by category | ❌ |
| GET | `/services/:id` | Get service by ID/fixedId | ❌ |
| POST | `/services` | Create service | ✅ |
| PUT | `/services/:id` | Update service | ✅ |
| DELETE | `/services/:id` | Delete service | ✅ |

### Services Features:
- **Icon & Image Upload**: S3 `services/` folder
- **Enhanced Structure**: description1, description2, howWeDoIt
- **Category System**: Title and description
- **Service Types**: Photography, Catering, Decoration, etc.
- **Q&A System**: Questions with answers and ordering
- **Pricing Information**: Starting price with currency
- **View Tracking**: Automatic view counting
- **Fixed ObjectID**: `service_1703123456789_def456ghi`

### Sample Service Request:
```json
POST /api/services (multipart/form-data)
{
  "title": "Wedding Photography",
  "description1": "Professional wedding photography services",
  "description2": "Capturing your special moments with artistic flair",
  "howWeDoIt": "Our team uses state-of-the-art equipment",
  "category": {
    "title": "Photography Services",
    "description": "Complete photography solutions"
  },
  "serviceType": "Photography",
  "importantPoints": [
    "Professional equipment",
    "Experienced photographers",
    "Same-day editing"
  ],
  "questionsAnswers": [
    {
      "question": "How many photos do we get?",
      "answer": "You'll receive 200-300 edited photos",
      "order": 1
    }
  ],
  "tags": ["wedding", "photography", "professional"],
  "pricing": {
    "startingPrice": 25000,
    "currency": "INR",
    "priceType": "project-based"
  },
  "icons": [FILE],
  "serviceImage": [FILE]
}
```

---

## 👥 Team Management

### Team Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/team` | Get all team members | ❌ |
| GET | `/team/:id` | Get team member by ID/fixedId | ❌ |
| POST | `/team` | Create team member | ✅ |
| PUT | `/team/:id` | Update team member | ✅ |
| DELETE | `/team/:id` | Delete team member | ✅ |

### Team Features:
- **Simplified Structure**: Only image and name
- **Image Upload**: S3 `team/` folder
- **Sort Ordering**: Custom ordering support
- **Fixed ObjectID**: `team_1703123456789_jkl789mno`

### Sample Team Request:
```json
POST /api/team (multipart/form-data)
{
  "name": "John Smith",
  "sortOrder": 1,
  "image": [FILE]
}
```

---

## 📝 Blog Management

### Blog Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/blogs` | Get all blog posts | ❌ |
| GET | `/blogs/category/:category` | Get blogs by category | ❌ |
| GET | `/blogs/:id` | Get blog by ID | ❌ |
| POST | `/blogs` | Create blog post | ✅ |
| PUT | `/blogs/:id` | Update blog post | ✅ |
| DELETE | `/blogs/:id` | Delete blog post | ✅ |

### Blog Features:
- **Image Upload**: S3 blog folder
- **Category System**: Organized content
- **Rich Content**: Title, content, excerpt
- **SEO Support**: Meta descriptions, tags
- **Status Management**: draft, published, archived

### Sample Blog Request:
```json
POST /api/blogs (multipart/form-data)
{
  "title": "Wedding Planning Tips",
  "content": "Complete guide to planning your perfect wedding...",
  "excerpt": "Essential tips for wedding planning",
  "category": "Wedding Tips",
  "tags": ["wedding", "planning", "tips"],
  "status": "published",
  "image": [FILE]
}
```

---

## 💬 Comments Management

### Comment Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/comments` | Create comment (Public) | ❌ |
| GET | `/comments/approved` | Get approved comments | ❌ |
| GET | `/comments/blog/:blogId` | Get comments by blog ID | ❌ |
| GET | `/comments/blog/:blogId/count` | Get comment count | ❌ |
| GET | `/comments` | Get all comments | ✅ |
| GET | `/comments/statistics` | Get comment statistics | ✅ |
| GET | `/comments/:id` | Get comment by ID | ✅ |
| PUT | `/comments/:id` | Update comment | ✅ |
| DELETE | `/comments/:id` | Delete comment | ✅ |

### Comment Features:
- **Public Submission**: No auth for posting
- **Moderation System**: Approval workflow
- **Blog Association**: Linked to specific blogs
- **Statistics**: Comment analytics

### Sample Comment Request:
```json
POST /api/comments
{
  "blogId": "blog_id_here",
  "name": "John Doe",
  "email": "<EMAIL>",
  "comment": "Great article! Very helpful.",
  "website": "https://johndoe.com"
}
```

---

## ❓ FAQ Management

### FAQ Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/faqs` | Get all FAQs | ❌ |
| GET | `/faqs/statistics` | Get FAQ statistics | ❌ |
| GET | `/faqs/category/:category` | Get FAQs by category | ❌ |
| GET | `/faqs/:id` | Get FAQ by ID | ❌ |
| POST | `/faqs` | Create FAQ | ✅ |
| PUT | `/faqs/:id` | Update FAQ | ✅ |
| DELETE | `/faqs/:id` | Delete FAQ | ✅ |

### FAQ Features:
- **Category Organization**: Grouped FAQs
- **Search Support**: Question/answer search
- **Priority Ordering**: Custom sorting
- **Statistics**: Usage analytics

### Sample FAQ Request:
```json
POST /api/faqs
{
  "question": "What services do you offer?",
  "answer": "We offer complete event management services including...",
  "category": "Services",
  "priority": 1,
  "isActive": true
}
```

---

## 📞 Contact Management

### Contact Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/contacts` | Create contact | ❌ |
| GET | `/contacts` | Get all contacts | ✅ |
| GET | `/contacts/statistics` | Get contact statistics | ✅ |
| GET | `/contacts/status/:status` | Get contacts by status | ✅ |
| GET | `/contacts/service/:service` | Get contacts by service | ✅ |
| GET | `/contacts/:id` | Get contact by ID | ✅ |
| PUT | `/contacts/:id` | Update contact | ✅ |
| DELETE | `/contacts/:id` | Delete contact | ✅ |

### Contact Features:
- **Public Submission**: No auth required
- **Status Tracking**: new, contacted, qualified, converted
- **Service Association**: Linked to specific services
- **Priority Management**: High, medium, low
- **Statistics**: Lead analytics

### Sample Contact Request:
```json
POST /api/contacts
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "9876543210",
  "service": "Wedding Planning",
  "message": "I need help planning my wedding",
  "eventDate": "2024-06-15",
  "budget": "100000-200000",
  "source": "website"
}
```

---

## 📋 Contact Form

### Contact Form Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/contact-form/submit` | Submit contact form | ❌ |

### Contact Form Features:
- **Simple Submission**: Basic contact form
- **Validation**: Email and phone validation
- **Auto-save**: Automatic database storage

### Sample Contact Form Request:
```json
POST /api/contact-form/submit
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "9876543210",
  "subject": "Wedding Inquiry",
  "message": "I would like to know more about your services"
}
```

---

## 📁 File Upload

### Upload Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/upload/single` | Upload single file | ❌ |
| POST | `/upload/multiple` | Upload multiple files | ❌ |

### Upload Features:
- **S3 Storage**: Direct AWS S3 upload
- **File Validation**: Type and size checks
- **Multiple Formats**: Images, documents, videos
- **Organized Storage**: Automatic folder structure

### Sample Upload Request:
```json
POST /api/upload/single (multipart/form-data)
{
  "file": [FILE]
}

Response:
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "filename": "1703123456789-file.jpg",
    "location": "https://s3.amazonaws.com/bucket/1703123456789-file.jpg",
    "bucket": "parvevent",
    "size": 1024000,
    "mimetype": "image/jpeg"
  }
}
```

---

## 🔧 Utility Endpoints

### System Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/test-cors` | Test CORS configuration | ❌ |
| GET | `/health` | Health check | ❌ |

---

## 📊 Response Format

All APIs follow consistent response format:

### Success Response:
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {...}
}
```

### Error Response:
```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"],
  "debug": "Stack trace (development only)"
}
```

### Pagination Response:
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

---

## 🔐 Authentication Headers

For protected endpoints, include JWT token:
```
Authorization: Bearer <your-jwt-token>
```

---

## 📁 S3 Folder Structure

```
parvevent-bucket/
├── gallery/          # Gallery images
├── reviews/          # Review images
├── hero-section/     # Hero section images
├── venues/           # Venue images
├── services/         # Service images and icons
├── team/             # Team member images
├── blogs/            # Blog images
└── uploads/          # General file uploads
```

---

## 🎯 Fixed ObjectID System

All entities use frontend-friendly fixed IDs for routing:
- **Gallery**: `gallery_1703123456789_abc123def`
- **Review**: `review_1703123456789_xyz789ghi`
- **Hero**: `hero_1703123456789_mno456pqr`
- **Venue**: `venue_1703123456789_stu123vwx`
- **Service**: `service_1703123456789_def456ghi`
- **Team**: `team_1703123456789_jkl789mno`
- **Blog**: `blog_1703123456789_pqr123stu`

---

## 🚀 Frontend Integration Examples

### React/Next.js Usage:
```javascript
// Fetch data
const gallery = await fetch('http://localhost:8005/api/gallery');
const venues = await fetch('http://localhost:8005/api/venues');
const services = await fetch('http://localhost:8005/api/services');

// Navigate using fixed IDs
router.push(`/gallery/${item.fixedId}`);
router.push(`/venues/${venue.fixedId}`);
router.push(`/services/${service.fixedId}`);

// Submit forms
const booking = await fetch('http://localhost:8005/api/venue-bookings', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(formData)
});

// Admin operations with auth
const response = await fetch('http://localhost:8005/api/gallery', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'multipart/form-data'
  },
  body: formData
});
```

---

## ✅ API Status Summary

### ✅ Working APIs:
- **Authentication**: 12 endpoints
- **Gallery**: 5 endpoints
- **Reviews**: 6 endpoints
- **Hero Section**: 6 endpoints
- **Venues**: 5 endpoints
- **Venue Bookings**: 6 endpoints
- **Services**: 8 endpoints
- **Team**: 5 endpoints
- **Blogs**: 6 endpoints
- **Comments**: 9 endpoints
- **FAQs**: 7 endpoints
- **Contacts**: 8 endpoints
- **Contact Form**: 1 endpoint
- **File Upload**: 2 endpoints
- **Utilities**: 2 endpoints

### **Total: 88+ API Endpoints**

**All APIs are tested, documented, and ready for production use! 🎉**
