const express = require("express");
const {
    createService,
    getAllServices,
    getServiceById,
    updateService,
    deleteService
} = require("../controllers/service.controller");
const { serviceUploadFields } = require("../config/awsclouds3");
const authMiddleware = require('../middleware/auth.middleware');
const router = express.Router();

// Apply security headers to all service routes
router.use(authMiddleware.securityHeaders);

// Public routes (no authentication required)
router.get("/", getAllServices);
router.get("/:id", getServiceById);

// Protected routes (authentication required)
router.post("/",
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    serviceUploadFields,
    createService
);

router.put("/:id",
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    serviceUploadFields,
    updateService
);

router.delete("/:id",
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    deleteService
);

module.exports = router;
