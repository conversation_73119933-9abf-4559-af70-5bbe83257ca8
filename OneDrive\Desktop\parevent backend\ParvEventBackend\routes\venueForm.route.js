const express = require('express');
const router = express.Router();
const path = require('path');
const {
    createVenueForm,
    getAllVenueForms,
    getVenueFormById,
    updateVenueForm,
    deleteVenueForm,
    getUpcomingEvents
} = require('../controllers/venueForm.controller');
const authMiddleware = require('../middleware/auth.middleware');

// Apply security headers to all venue form routes
router.use(authMiddleware.securityHeaders);

// View page route (admin only)
router.get('/view',
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    (req, res) => {
        res.sendFile(path.join(__dirname, '../views/venue-booking-view.html'));
    }
);

// Public routes (no authentication required)
router.post('/', createVenueForm);

// Protected routes (authentication required)
router.get('/', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    getAllVenueForms
);

router.get('/upcoming', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    getUpcomingEvents
);

router.get('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    getVenueFormById
);

router.put('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    updateVenueForm
);

router.delete('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    deleteVenueForm
);

module.exports = router;
