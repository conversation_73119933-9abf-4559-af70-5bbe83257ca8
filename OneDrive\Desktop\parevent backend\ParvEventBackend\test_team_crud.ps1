$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTU1ODgxLCJleHAiOjE3NTExNTk0ODF9.GPbgqGvLiAH_0CeNZnkMcomH47jaTMAssmL4Zmhiokw'
}

Write-Host "=== Testing Team CRUD with ObjectId ==="

# Test 1: Create Team Member (without image)
Write-Host "`n1. Testing Team Member Creation..."
$teamData = @{
    name = "Test Team Member"
    sortOrder = 1
} | ConvertTo-Json

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/team' -Method POST -Headers $headers -ContentType 'application/json' -Body $teamData
    Write-Host "SUCCESS: Team member created!"
    Write-Host "Team ID: $($result.data._id)"
    Write-Host "Name: $($result.data.name)"
    $teamId = $result.data._id
    
    # Test 2: Get Team Member by ObjectId
    Write-Host "`n2. Testing Get Team Member by ObjectId..."
    try {
        $getResult = Invoke-RestMethod -Uri "http://localhost:8005/api/team/$teamId" -Headers $headers
        Write-Host "SUCCESS: Retrieved team member with ObjectId"
        Write-Host "Name: $($getResult.data.name)"
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)"
    }
    
    # Test 3: Update Team Member
    Write-Host "`n3. Testing Update Team Member..."
    $updateData = @{
        name = "Updated Team Member Name"
        sortOrder = 2
    } | ConvertTo-Json
    
    try {
        $updateResult = Invoke-RestMethod -Uri "http://localhost:8005/api/team/$teamId" -Method PUT -Headers $headers -ContentType 'application/json' -Body $updateData
        Write-Host "SUCCESS: Team member updated"
        Write-Host "New name: $($updateResult.data.name)"
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)"
    }
    
    # Test 4: Delete Team Member
    Write-Host "`n4. Testing Delete Team Member..."
    try {
        $deleteResult = Invoke-RestMethod -Uri "http://localhost:8005/api/team/$teamId" -Method DELETE -Headers $headers
        Write-Host "SUCCESS: Team member deleted"
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)"
    }
    
} catch {
    Write-Host "ERROR creating team member: $($_.Exception.Message)"
}

# Test 5: Get All Team Members
Write-Host "`n5. Testing Get All Team Members..."
try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/team' -Headers $headers
    Write-Host "SUCCESS: Found $($result.data.teamMembers.Count) team members"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}

Write-Host "`n=== Team CRUD Test Complete ==="
