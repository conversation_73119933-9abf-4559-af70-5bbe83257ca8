const express = require('express');
const router = express.Router();
const {
    signup,
    verifyEmail,
    verify2FA,
    login,
    logout,
    resendEmailOTP,
    getProfile,
    simpleLogin,
    debugAdmins,
    directLogin,
    emailLogin,
    quickLogin,
    testSignup
} = require('../controllers/auth.controller');
const authMiddleware = require('../middleware/auth.middleware');

// Apply security headers to all auth routes
router.use(authMiddleware.securityHeaders);

// Apply rate limiting to auth routes
const authRateLimit = authMiddleware.createRateLimit();

// Public routes (no authentication required)
router.post('/signup', authRateLimit, signup);
router.post('/signup-test', signup); // No rate limiting for testing
router.post('/test-signup', testSignup); // Test endpoint without database
router.post('/verify-email', authRateLimit, verifyEmail);
router.post('/verify-2fa', authRateLimit, verify2FA);
router.post('/login', authRateLimit, login);
router.post('/login-simple', simpleLogin); // Simple login for testing
router.post('/login-direct', directLogin); // Direct login bypassing encryption
router.post('/login-email', emailLogin); // Email-based smart login
router.post('/login-quick', quickLogin); // Quick login by admin ID
router.post('/resend-otp', authRateLimit, resendEmailOTP);

// Protected routes (authentication required)
router.post('/logout', authMiddleware.authenticateAdmin, logout);
router.get('/profile', authMiddleware.authenticateAdmin, getProfile);

// Debug endpoint to list all admins
router.get('/debug-admins', debugAdmins);

// Health check for auth service
router.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Auth service is running',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;
