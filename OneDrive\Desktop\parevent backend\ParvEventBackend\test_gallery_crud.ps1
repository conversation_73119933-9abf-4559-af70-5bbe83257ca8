$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTU1ODgxLCJleHAiOjE3NTExNTk0ODF9.GPbgqGvLiAH_0CeNZnkMcomH47jaTMAssmL4Zmhiokw'
}

Write-Host "=== Testing Gallery CRUD with ObjectId ==="

# Test 1: Get All Gallery Items
Write-Host "`n1. Testing Get All Gallery Items..."
try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/gallery' -Headers $headers
    Write-Host "SUCCESS: Found $($result.data.galleries.Count) gallery items"
    
    if ($result.data.galleries.Count -gt 0) {
        $galleryId = $result.data.galleries[0]._id
        Write-Host "Using existing gallery ID: $galleryId"
        
        # Test 2: Get Gallery by ObjectId
        Write-Host "`n2. Testing Get Gallery by ObjectId..."
        try {
            $getResult = Invoke-RestMethod -Uri "http://localhost:8005/api/gallery/$galleryId" -Headers $headers
            Write-Host "SUCCESS: Retrieved gallery item with ObjectId"
            Write-Host "Title: $($getResult.data.title)"
        } catch {
            Write-Host "ERROR: $($_.Exception.Message)"
        }
        
        # Test 3: Update Gallery Item
        Write-Host "`n3. Testing Update Gallery Item..."
        $updateData = @{
            title = "Updated Gallery Title"
            description = "Updated description"
            keywords = @("updated", "test", "gallery")
        } | ConvertTo-Json
        
        try {
            $updateResult = Invoke-RestMethod -Uri "http://localhost:8005/api/gallery/$galleryId" -Method PUT -Headers $headers -ContentType 'application/json' -Body $updateData
            Write-Host "SUCCESS: Gallery item updated"
            Write-Host "New title: $($updateResult.data.title)"
        } catch {
            Write-Host "ERROR: $($_.Exception.Message)"
        }
        
        # Test 4: Delete Gallery Item
        Write-Host "`n4. Testing Delete Gallery Item..."
        try {
            $deleteResult = Invoke-RestMethod -Uri "http://localhost:8005/api/gallery/$galleryId" -Method DELETE -Headers $headers
            Write-Host "SUCCESS: Gallery item deleted"
        } catch {
            Write-Host "ERROR: $($_.Exception.Message)"
        }
    } else {
        Write-Host "No existing gallery items found to test with"
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}

Write-Host "`n=== Gallery CRUD Test Complete ==="
