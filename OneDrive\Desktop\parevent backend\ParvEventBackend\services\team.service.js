const Team = require("../models/team.model");
const { s3Client } = require("../config/awsclouds3");
const { DeleteObjectCommand } = require("@aws-sdk/client-s3");

// Create a new team member
const createTeamMember = async (teamData, file) => {
  try {
    const { name, jobCategory, socialMedia, status } = teamData;

    // Validate required fields
    if (!name || !jobCategory) {
      throw new Error("Name and job category are required");
    }

    // Prepare team member data
    const newTeamData = {
      name: name.trim(),
      jobCategory: jobCategory.trim(),
      status: status || "active"
    };

    // Handle social media links
    if (socialMedia) {
      const parsedSocialMedia = typeof socialMedia === 'string' ? JSON.parse(socialMedia) : socialMedia;
      newTeamData.socialMedia = {
        website: parsedSocialMedia.website || null,
        facebook: parsedSocialMedia.facebook || null,
        linkedin: parsedSocialMedia.linkedin || null,
        instagram: parsedSocialMedia.instagram || null,
        github: parsedSocialMedia.github || null
      };
    }

    // Handle image upload
    if (file) {
      newTeamData.imageUrl = file.location; // S3 URL
    }

    // Create and save team member
    const teamMember = new Team(newTeamData);
    const savedTeamMember = await teamMember.save();

    return savedTeamMember;
  } catch (error) {
    // If there was an error and a file was uploaded, clean it up
    if (file && file.key) {
      await deleteImageFromS3(file.key);
    }
    throw error;
  }
};

// Get all team members with filtering and pagination
const getAllTeamMembers = async (queryParams = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      jobCategory,
      status = "active",
      search,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = queryParams;

    // Build filter object
    const filter = { status };

    if (jobCategory) {
      filter.jobCategory = jobCategory;
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { jobCategory: { $regex: search, $options: "i" } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query with pagination
    const teamMembers = await Team.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalCount = await Team.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    return {
      teamMembers,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

// Get team member by ID
const getTeamMemberById = async (id) => {
  try {
    const teamMember = await Team.findOne({ id: parseInt(id) });
    return teamMember;
  } catch (error) {
    throw error;
  }
};

// Update team member
const updateTeamMember = async (id, updateData, file) => {
  try {
    const existingTeamMember = await Team.findOne({ id: parseInt(id) });
    if (!existingTeamMember) {
      throw new Error("Team member not found");
    }

    // Prepare update data
    const updatedData = { ...updateData };

    // Handle social media links
    if (updateData.socialMedia) {
      const parsedSocialMedia = typeof updateData.socialMedia === 'string' 
        ? JSON.parse(updateData.socialMedia) 
        : updateData.socialMedia;
      
      updatedData.socialMedia = {
        website: parsedSocialMedia.website || null,
        facebook: parsedSocialMedia.facebook || null,
        linkedin: parsedSocialMedia.linkedin || null,
        instagram: parsedSocialMedia.instagram || null,
        github: parsedSocialMedia.github || null
      };
    }

    // Handle image update
    if (file) {
      // Delete old image if exists (extract key from existing imageUrl)
      if (existingTeamMember.imageUrl) {
        const oldImageKey = extractS3KeyFromUrl(existingTeamMember.imageUrl);
        if (oldImageKey) {
          await deleteImageFromS3(oldImageKey);
        }
      }

      updatedData.imageUrl = file.location;
    }

    // Update team member
    const updatedTeamMember = await Team.findOneAndUpdate(
      { id: parseInt(id) },
      updatedData,
      { new: true, runValidators: true }
    );

    return updatedTeamMember;
  } catch (error) {
    // If there was an error and a new file was uploaded, clean it up
    if (file && file.key) {
      await deleteImageFromS3(file.key);
    }
    throw error;
  }
};

// Delete team member
const deleteTeamMember = async (id) => {
  try {
    const teamMember = await Team.findOne({ id: parseInt(id) });
    if (!teamMember) {
      throw new Error("Team member not found");
    }

    // Delete image from S3 if exists (extract key from imageUrl)
    if (teamMember.imageUrl) {
      const imageKey = extractS3KeyFromUrl(teamMember.imageUrl);
      if (imageKey) {
        await deleteImageFromS3(imageKey);
      }
    }

    // Delete team member from database
    await Team.findOneAndDelete({ id: parseInt(id) });

    return { message: "Team member deleted successfully" };
  } catch (error) {
    throw error;
  }
};

// Get team members by job category
const getTeamMembersByCategory = async (jobCategory, queryParams = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      status = "active",
      search,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = queryParams;

    // Build filter object
    const filter = { jobCategory, status };

    if (search) {
      filter.name = { $regex: search, $options: "i" };
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query
    const teamMembers = await Team.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count
    const totalCount = await Team.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    return {
      teamMembers,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

// Helper function to extract S3 key from URL
const extractS3KeyFromUrl = (imageUrl) => {
  try {
    if (!imageUrl) return null;

    // Extract key from S3 URL
    // URL format: https://bucket-name.s3.region.amazonaws.com/team/filename.jpg
    const url = new URL(imageUrl);
    const key = url.pathname.substring(1); // Remove leading slash
    return key;
  } catch (error) {
    console.error('Error extracting S3 key from URL:', error);
    return null;
  }
};

// Helper function to delete image from S3
const deleteImageFromS3 = async (imageKey) => {
  try {
    if (!imageKey) return;

    const deleteParams = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: imageKey
    };

    const command = new DeleteObjectCommand(deleteParams);
    await s3Client.send(command);
    console.log(`✅ Image deleted from S3: ${imageKey}`);
  } catch (error) {
    console.error(`❌ Error deleting image from S3: ${imageKey}`, error);
    // Don't throw error here as it shouldn't stop the main operation
  }
};

module.exports = {
  createTeamMember,
  getAllTeamMembers,
  getTeamMemberById,
  updateTeamMember,
  deleteTeamMember,
  getTeamMembersByCategory
};
