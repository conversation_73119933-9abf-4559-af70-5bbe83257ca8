const express = require("express");
const {
    createTeamMember,
    getAllTeamMembers,
    getTeamMemberById,
    updateTeamMember,
    deleteTeamMember
} = require("../controllers/team.controller");
const { teamUploadSingle } = require("../config/awsclouds3");
const authMiddleware = require('../middleware/auth.middleware');
const router = express.Router();

// Apply security headers to all team routes
router.use(authMiddleware.securityHeaders);

// Public routes (no authentication required)
router.get("/", getAllTeamMembers);
router.get("/:id", getTeamMemberById);

// Protected routes (authentication required)
router.post("/",
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    teamUploadSingle('image'),
    createTeamMember
);

router.put("/:id",
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    teamUploadSingle('image'),
    updateTeamMember
);

router.delete("/:id",
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    deleteTeamMember
);

module.exports = router;
