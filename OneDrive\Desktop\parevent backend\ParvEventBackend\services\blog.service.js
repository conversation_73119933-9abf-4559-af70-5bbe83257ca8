const Blog = require("../models/blog.model.js");
const { DeleteObjectCommand } = require('@aws-sdk/client-s3');
const { s3Client } = require('../config/awsclouds3');

// Create a new blog
const createBlog = async (body, imageFile = null) => {
  const { title, description, category, keywords } = body;

  try {
    // Parse keywords if it's a string
    let parsedKeywords = [];
    if (keywords) {
      if (typeof keywords === 'string') {
        parsedKeywords = keywords.split(',').map(keyword => keyword.trim().toLowerCase()).filter(k => k);
      } else if (Array.isArray(keywords)) {
        parsedKeywords = keywords.map(keyword => keyword.trim().toLowerCase()).filter(k => k);
      }
    }

    // Parse category if it's a string
    let parsedCategory = [];
    if (category) {
      if (typeof category === 'string') {
        parsedCategory = category.split(',').map(cat => cat.trim().toLowerCase()).filter(c => c);
      } else if (Array.isArray(category)) {
        parsedCategory = category.map(cat => cat.trim().toLowerCase()).filter(c => c);
      }
    }

    const blogData = {
      title: title?.trim(),
      description: description, // Don't trim description to preserve formatting
      category: parsedCategory,
      keywords: parsedKeywords
    };

    // Add image information if file was uploaded
    if (imageFile) {
      blogData.imageUrl = imageFile.location;
    }

    const blog = await Blog.create(blogData);
    return blog;
  } catch (error) {
    console.error("Error creating blog:", error);
    throw error;
  }
};

// Get all blogs with filtering and search
const getAllBlogs = async (query) => {
  const {
    page = 1,
    pageSize = 40,
    sortBy = "createdAt",
    sortOrder = "desc",
    category,
    search,
    keywords
  } = query;

  const offset = (page - 1) * pageSize;
  const sortOrderValue = sortOrder.toLowerCase() === "desc" ? -1 : 1;

  try {
    // Build filter object
    const filter = {};

    // Filter by category (now supports array)
    if (category) {
      if (typeof category === 'string') {
        filter.category = { $in: [new RegExp(category, 'i')] };
      } else if (Array.isArray(category)) {
        filter.category = { $in: category.map(cat => new RegExp(cat, 'i')) };
      }
    }

    // Search functionality (updated for category array)
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { keywords: { $in: [new RegExp(search, 'i')] } },
        { category: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Filter by keywords
    if (keywords) {
      const keywordArray = typeof keywords === 'string' ? keywords.split(',').map(k => k.trim()) : keywords;
      filter.keywords = { $in: keywordArray.map(k => new RegExp(k, 'i')) };
    }

    const totalCount = await Blog.countDocuments(filter);
    const blogs = await Blog.find(filter)
      .sort({ [sortBy]: sortOrderValue })
      .skip(offset)
      .limit(Number(pageSize));

    const totalPages = Math.ceil(totalCount / pageSize);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      data: blogs,
      totalCount,
      totalPages,
      hasNextPage,
      hasPreviousPage,
      currentPage: Number(page),
      pageSize: Number(pageSize),
      filters: {
        category,
        search,
        keywords
      }
    };
  } catch (error) {
    console.error("Error fetching blogs:", error);
    throw error;
  }
};

// Get a blog by id
const getBlogById = async (id) => {
  try {
    const blog = await Blog.findById(id);
    return blog;
  } catch (error) {
    console.error("Error fetching blog:", error);
    throw error;
  }
};

// Update a blog by id
const updateBlog = async (id, body, imageFile = null) => {
  try {
    const existingBlog = await Blog.findById(id);
    if (!existingBlog) {
      throw new Error('Blog not found');
    }

    // Parse keywords if it's a string
    let parsedKeywords = existingBlog.keywords;
    if (body.keywords !== undefined) {
      if (typeof body.keywords === 'string') {
        parsedKeywords = body.keywords.split(',').map(keyword => keyword.trim().toLowerCase()).filter(k => k);
      } else if (Array.isArray(body.keywords)) {
        parsedKeywords = body.keywords.map(keyword => keyword.trim().toLowerCase()).filter(k => k);
      }
    }

    // Parse category if it's a string
    let parsedCategory = existingBlog.category;
    if (body.category !== undefined) {
      if (typeof body.category === 'string') {
        parsedCategory = body.category.split(',').map(cat => cat.trim().toLowerCase()).filter(c => c);
      } else if (Array.isArray(body.category)) {
        parsedCategory = body.category.map(cat => cat.trim().toLowerCase()).filter(c => c);
      }
    }

    const updateData = {
      ...body,
      category: parsedCategory,
      keywords: parsedKeywords
    };

    // Preserve description formatting - don't trim
    if (body.title) {
      updateData.title = body.title.trim();
    }
    // Description is kept as-is to preserve formatting

    // Handle image update
    if (imageFile) {
      updateData.imageUrl = imageFile.location;
    }

    const blog = await Blog.findByIdAndUpdate(id, updateData, { new: true, runValidators: true });
    return blog;
  } catch (error) {
    console.error("Error updating blog:", error);
    throw error;
  }
};

// Delete a blog by id
const deleteBlog = async (id) => {
  try {
    const blog = await Blog.findByIdAndDelete(id);
    if (!blog) {
      throw new Error('Blog not found');
    }
    return blog;
  } catch (error) {
    console.error("Error deleting blog:", error);
    throw error;
  }
};

// Get blogs by category
const getBlogsByCategory = async (category, query = {}) => {
  const {
    page = 1,
    pageSize = 10,
    sortBy = "createdAt",
    sortOrder = "desc"
  } = query;

  const offset = (page - 1) * pageSize;
  const sortOrderValue = sortOrder.toLowerCase() === "desc" ? -1 : 1;

  try {
    // Updated filter for category array
    const filter = { category: { $in: [new RegExp(category, 'i')] } };

    const totalCount = await Blog.countDocuments(filter);
    const blogs = await Blog.find(filter)
      .sort({ [sortBy]: sortOrderValue })
      .skip(offset)
      .limit(Number(pageSize));

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      data: blogs,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      currentPage: Number(page),
      category
    };
  } catch (error) {
    console.error("Error fetching blogs by category:", error);
    throw error;
  }
};

// Increment blog views
const incrementBlogViews = async (id) => {
  try {
    const blog = await Blog.findByIdAndUpdate(
      id,
      { $inc: { views: 1 } },
      { new: true }
    );
    return blog;
  } catch (error) {
    console.error("Error incrementing blog views:", error);
    throw error;
  }
};

module.exports = {
  createBlog,
  getAllBlogs,
  getBlogById,
  updateBlog,
  deleteBlog,
  getBlogsByCategory,
  incrementBlogViews
};
