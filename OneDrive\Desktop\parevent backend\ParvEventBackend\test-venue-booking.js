// Test script for venue booking API
const testVenueBooking = {
  fullName: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '9876543210',
  venueId: '6860b6ba90c4690ef08a8084', // Replace with actual venue ID
  venueName: 'Test Venue',
  dateOfPlan: '2025-07-15',
  message: 'I need this venue for a wedding ceremony with 200 guests. Please provide catering and decoration services.'
};

console.log('Test venue booking data:');
console.log(JSON.stringify(testVenueBooking, null, 2));

// To test, send POST request to: /venue-bookings
// curl -X POST http://localhost:8005/venue-bookings \
//   -H "Content-Type: application/json" \
//   -d '{"fullName":"John Doe","email":"<EMAIL>","phoneNumber":"9876543210","venueId":"6860b6ba90c4690ef08a8084","venueName":"Test Venue","dateOfPlan":"2025-07-15","message":"Test booking message"}'
