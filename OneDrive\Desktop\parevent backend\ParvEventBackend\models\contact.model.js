const mongoose = require("mongoose");
const validator = require("validator");

const ContactSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Name is required"],
      trim: true,
      maxlength: [100, "Name cannot exceed 100 characters"]
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      trim: true,
      lowercase: true,
      validate: {
        validator: (value) => validator.isEmail(value),
        message: "Invalid email format",
      },
    },
    countryCode: {
      type: String,
      required: [true, "Country code is required"],
      default: "+91",
      trim: true
    },
    phoneNumber: {
      type: String,
      required: [true, "Phone number is required"],
      trim: true,
      validate: {
        validator: function(value) {
          // Allow 10-15 digits
          return /^[\d\s\-]{8,15}$/.test(value.replace(/\s|-/g, ''));
        },
        message: "Invalid phone number format",
      },
    },
    // Virtual field for full phone number
    phone: {
      type: String,
      virtual: true,
      get: function() {
        return `${this.countryCode}${this.phoneNumber}`;
      }
    },
    service: {
      type: String,
      required: [true, "Service is required"]
    },
    message: {
      type: String,
      required: [true, "Message is required"],
      trim: true,
      maxlength: [1000, "Message cannot exceed 1000 characters"]
    },
    status: {
      type: String,
      enum: ["new", "in-progress", "resolved", "closed"],
      default: "new"
    },
    priority: {
      type: String,
      enum: ["low", "medium", "high", "urgent"],
      default: "medium"
    }
  },
  { 
    timestamps: true,
    toJSON: {
      transform: (doc, ret) => {
        // Convert timestamps to IST
        ret.createdAt = new Date(ret.createdAt).toLocaleString("en-IN", { 
          timeZone: "Asia/Kolkata",
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        ret.updatedAt = new Date(ret.updatedAt).toLocaleString("en-IN", { 
          timeZone: "Asia/Kolkata",
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        return ret;
      }
    }
  }
);

// Add indexes for better query performance
ContactSchema.index({ email: 1 });
ContactSchema.index({ status: 1 });
ContactSchema.index({ service: 1 });
ContactSchema.index({ createdAt: -1 });

module.exports = mongoose.model("Contact", ContactSchema);
