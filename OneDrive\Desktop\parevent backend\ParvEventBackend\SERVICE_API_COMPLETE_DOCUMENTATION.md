# 🛠️ Service API - Complete Documentation

## Overview
Complete Service Management API documentation with detailed SVG icon system, fallback options, and website integration examples.

## 🏗️ System Architecture

### Core Components
- **Model**: `models/service.model.js` - Service data structure
- **Controller**: `controllers/service.controller.js` - Request handling
- **Routes**: `routes/service.route.js` - API endpoints
- **Base URL**: `/services`

## 📊 Database Schema

### Service Model (Updated with Icon Options)
```javascript
{
  _id: ObjectId,
  title: String (required, max 200 chars),
  description: String (required, max 1000 chars),
  description2: String (optional, max 1000 chars),
  
  // ICON SYSTEM - DUAL OPTIONS
  icons: String (optional, SVG file S3 URL),
  iconImage: String (optional, regular image S3 URL), // NEW FALLBACK OPTION
  displayIcon: String (virtual field - best available icon), // NEW SMART FIELD
  
  image: String (required, service main image S3 URL),
  howWeDoIt: [
    {
      title: String (max 200 chars),
      description: String (max 2000 chars)
    }
  ],
  isActive: Boolean (default: true),
  sortOrder: Number (default: 0),
  createdAt: Date,
  updatedAt: Date
}
```

## 🎨 Icon System - Complete Guide

### 🔧 IMPORTANT CHANGES MADE:
**Problem**: SVG icons were not displaying properly (downloading instead of showing)
**Solution**: Added dual icon system with image fallback option

### Icon Options Available:

#### **Option 1: SVG Icons (Primary)**
- **Field**: `icons`
- **File Type**: SVG files (.svg)
- **Storage**: `services/icons/` folder in S3
- **MIME Types**: `image/svg+xml`, `image/svg`
- **File Size**: 10MB maximum
- **Benefits**: Scalable, lightweight, customizable with CSS
- **Issue**: May not display properly in some browsers/configurations

#### **Option 2: Image Icons (Fallback) - NEW**
- **Field**: `iconImage`
- **File Type**: PNG, JPG, GIF, WebP
- **Storage**: `services/icon-images/` folder in S3
- **MIME Types**: `image/jpeg`, `image/png`, `image/gif`, `image/webp`
- **File Size**: 10MB maximum
- **Benefits**: Always displays properly, no browser compatibility issues
- **Usage**: Use when SVG icons don't work

#### **Option 3: Smart Display (Virtual Field) - NEW**
- **Field**: `displayIcon`
- **Logic**: Returns `iconImage` if available, otherwise returns `icons`
- **Usage**: Use this field in frontend for automatic best icon selection
- **Benefits**: Automatically handles fallback logic

### Icon Upload Configuration:
```javascript
// S3 Upload Fields (Updated)
serviceUploadFields: multer().fields([
  { name: 'icons', maxCount: 1 },      // SVG icons
  { name: 'iconImage', maxCount: 1 },  // Image icons (NEW)
  { name: 'image', maxCount: 1 }       // Service main image
])
```

## 🔗 API Endpoints

### 1. Create Service (Admin)
```http
POST /services
Content-Type: multipart/form-data
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:**
```javascript
{
  "title": "String (required, max 200 chars)",
  "description": "String (required, max 1000 chars)",
  "description2": "String (optional, max 1000 chars)",
  "howWeDoIt": [
    {
      "title": "String (max 200 chars)",
      "description": "String (max 2000 chars)"
    }
  ],
  "sortOrder": "Number (optional, default: 0)",
  
  // FILE UPLOADS (at least one icon required)
  "icons": "File (SVG icon - optional)",
  "iconImage": "File (Image icon - optional)", // NEW OPTION
  "image": "File (Service main image - required)"
}
```

**Response:**
```javascript
{
  "success": true,
  "message": "Service created successfully",
  "data": {
    "_id": "ObjectId",
    "title": "String",
    "description": "String",
    "description2": "String",
    "icons": "String (SVG S3 URL or null)",
    "iconImage": "String (Image S3 URL or null)",
    "displayIcon": "String (Best available icon URL)",
    "image": "String (Service image S3 URL)",
    "howWeDoIt": [
      {
        "title": "String",
        "description": "String"
      }
    ],
    "isActive": true,
    "sortOrder": "Number",
    "createdAt": "Date",
    "updatedAt": "Date"
  }
}
```

### 2. Get All Services (Public)
```http
GET /services?page=1&limit=10&search=wedding&sortBy=sortOrder&sortOrder=asc
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search in title, description, description2
- `sortBy`: Sort field (default: createdAt)
- `sortOrder`: asc/desc (default: desc)

**Response:**
```javascript
{
  "success": true,
  "data": {
    "services": [
      {
        "_id": "ObjectId",
        "title": "String",
        "description": "String",
        "description2": "String",
        "icons": "String (SVG URL or null)",
        "iconImage": "String (Image URL or null)",
        "displayIcon": "String (Best available icon)",
        "image": "String (Service image URL)",
        "howWeDoIt": [
          {
            "title": "String",
            "description": "String"
          }
        ],
        "isActive": true,
        "sortOrder": "Number",
        "createdAt": "Date",
        "updatedAt": "Date"
      }
    ],
    "pagination": {
      "currentPage": "Number",
      "totalPages": "Number",
      "totalItems": "Number",
      "itemsPerPage": "Number",
      "hasNextPage": "Boolean",
      "hasPrevPage": "Boolean"
    }
  }
}
```

### 3. Get Service by ID (Public)
```http
GET /services/:id
```

**Response:** Same as individual service object above

### 4. Update Service (Admin)
```http
PUT /services/:id
Content-Type: multipart/form-data
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:** Same as create, all fields optional

### 5. Delete Service (Admin)
```http
DELETE /services/:id
Authorization: Bearer <JWT_TOKEN>
```

## 💡 Frontend Integration - Icon Display Options

### Option 1: Use SVG Icons (If Working)
```javascript
// Basic SVG icon usage
<img src="${service.icons}" 
     alt="${service.title} Icon" 
     class="service-icon">
```

### Option 2: Use Image Icons (Guaranteed to Work)
```javascript
// Fallback image icon usage
<img src="${service.iconImage}" 
     alt="${service.title} Icon" 
     class="service-icon">
```

### Option 3: Use Smart Display (Recommended)
```javascript
// Automatic best icon selection
<img src="${service.displayIcon}" 
     alt="${service.title} Icon" 
     class="service-icon">
```

### Option 4: Complete Fallback System (Best Practice)
```html
<!-- Complete service display with full fallback -->
<div class="service-card">
  <div class="service-icon-container">
    <img src="${service.displayIcon || service.iconImage || service.icons}" 
         alt="${service.title} Icon" 
         class="service-icon"
         onerror="handleIconError(this, '${service.title}')">
  </div>
  <h5>${service.title}</h5>
  <p>${service.description}</p>
</div>

<style>
.service-icon-container {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
  padding: 15px;
}

.service-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.icon-fallback {
  background: #6c757d;
  color: white;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
}
</style>

<script>
// Handle icon loading errors
function handleIconError(img, serviceTitle) {
  console.warn('Service icon failed to load:', img.src);
  img.style.display = 'none';
  img.parentElement.innerHTML = `
    <div class="icon-fallback">
      ${serviceTitle.charAt(0).toUpperCase()}
    </div>
  `;
}

// Fetch and display services
async function loadServices() {
  try {
    const response = await fetch('/services?limit=20&sortBy=sortOrder&sortOrder=asc');
    const result = await response.json();
    
    if (result.success) {
      displayServices(result.data.services);
    }
  } catch (error) {
    console.error('Error loading services:', error);
  }
}

function displayServices(services) {
  const container = document.getElementById('services-container');
  container.innerHTML = services.map(service => `
    <div class="col-md-4 mb-4">
      <div class="card service-card h-100">
        <div class="card-body text-center">
          <div class="service-icon-container">
            <img src="${service.displayIcon || service.iconImage || service.icons}" 
                 alt="${service.title} Icon" 
                 class="service-icon"
                 onerror="handleIconError(this, '${service.title}')">
          </div>
          <h5 class="card-title">${service.title}</h5>
          <p class="card-text">${service.description}</p>
          ${service.description2 ? `<p class="text-muted">${service.description2}</p>` : ''}
          
          ${service.howWeDoIt && service.howWeDoIt.length > 0 ? `
            <div class="how-we-do-it mt-3">
              <h6>How We Do It:</h6>
              ${service.howWeDoIt.map(item => `
                <div class="mb-2">
                  <strong>${item.title}</strong>
                  <p class="small">${item.description}</p>
                </div>
              `).join('')}
            </div>
          ` : ''}
        </div>
      </div>
    </div>
  `).join('');
}

// Load services when page loads
document.addEventListener('DOMContentLoaded', loadServices);
</script>
```

## 🚀 Admin Panel Usage Guide

### Creating Services with Icons:

#### **Method 1: SVG Icon Only**
1. Upload SVG file to `icons` field
2. Upload service image to `image` field
3. Fill other details
4. Submit

#### **Method 2: Image Icon Only**
1. Upload PNG/JPG file to `iconImage` field
2. Upload service image to `image` field
3. Fill other details
4. Submit

#### **Method 3: Both Icons (Recommended)**
1. Upload SVG file to `icons` field
2. Upload PNG/JPG file to `iconImage` field
3. Upload service image to `image` field
4. Fill other details
5. Submit

### Icon Selection Logic:
- If both icons uploaded: `displayIcon` returns `iconImage` (more reliable)
- If only SVG uploaded: `displayIcon` returns `icons`
- If only image uploaded: `displayIcon` returns `iconImage`

## 🔍 Testing & Debugging

### Test Icon Display:
```javascript
// Check icon availability for each service
services.forEach(service => {
  console.log(`Service: ${service.title}`);
  console.log(`  SVG Icon: ${service.icons || 'None'}`);
  console.log(`  Image Icon: ${service.iconImage || 'None'}`);
  console.log(`  Display Icon: ${service.displayIcon || 'None'}`);
});
```

### Test File Available:
- **File**: `test-service-icons-fixed.html`
- **Purpose**: Test both SVG and image icons with fallback behavior
- **Features**: Shows icon loading status, fallback display, error handling

## 🎯 Best Practices

### For Developers:
1. **Always use `displayIcon`** for frontend display
2. **Implement error handling** for failed icon loads
3. **Provide fallback display** (service title initial)
4. **Test both icon types** during development

### For Admins:
1. **Upload both icon types** for maximum compatibility
2. **Test icon display** after uploading
3. **Use image icons** if SVG doesn't work
4. **Optimize file sizes** before uploading

### For Performance:
1. **Optimize images** before upload
2. **Use appropriate file formats** (SVG for simple icons, PNG for complex)
3. **Implement lazy loading** for better performance
4. **Cache icon URLs** when possible

## 🔧 Troubleshooting

### SVG Icons Not Displaying:
1. **Check file format**: Ensure it's a valid SVG file
2. **Try image icon**: Upload PNG/JPG to `iconImage` field
3. **Use displayIcon**: Use `service.displayIcon` in frontend
4. **Check S3 permissions**: Ensure files are publicly accessible

### No Icons Showing:
1. **Check API response**: Verify icon URLs are present
2. **Test URLs directly**: Open icon URLs in browser
3. **Check error handling**: Implement `onerror` handlers
4. **Use fallback display**: Show service title initial

This documentation provides complete coverage of the Service API with detailed icon system options and implementation examples.
