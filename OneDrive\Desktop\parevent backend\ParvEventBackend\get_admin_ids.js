const mongoose = require('mongoose');
const Admin = require('./models/admin.model');
require('dotenv').config();

async function getAdminIds() {
    try {
        // Connect to database
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to database');

        // Get all admins with basic info
        const allAdmins = await Admin.find({}).select('_id createdAt isVerified otpVerified isActive');
        console.log(`📋 Found ${allAdmins.length} admins:`);

        allAdmins.forEach((admin, index) => {
            console.log(`${index + 1}. ID: ${admin._id}`);
            console.log(`   Created: ${admin.createdAt}`);
            console.log(`   isVerified: ${admin.isVerified}`);
            console.log(`   otpVerified: ${admin.otpVerified}`);
            console.log(`   isActive: ${admin.isActive}`);
            console.log('---');
        });

        // Update the latest admin (most recently created)
        if (allAdmins.length > 0) {
            const latestAdmin = allAdmins.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];
            console.log(`🎯 Updating latest admin: ${latestAdmin._id}`);
            
            await Admin.findByIdAndUpdate(latestAdmin._id, {
                isVerified: true,
                otpVerified: true,
                isActive: true
            });
            
            console.log('✅ Latest admin updated successfully!');
            console.log(`📋 Admin ID to use for login: ${latestAdmin._id}`);
        }

        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

getAdminIds();
