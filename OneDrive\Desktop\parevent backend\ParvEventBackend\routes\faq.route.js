const express = require("express");
const {
  createFAQ,
  getAllFAQs,
  getFAQById,
  updateFAQ,
  deleteFAQ,
  getFAQsByCategory,
  getFAQStatistics
} = require("../controllers/faq.controller");
const router = express.Router();

// FAQ CRUD routes

// POST - Create new FAQ
router.post("/faqs", createFAQ);

// GET - Get all FAQs with filtering and pagination
router.get("/faqs", getAllFAQs);

// GET - Get FAQ statistics
router.get("/faqs/statistics", getFAQStatistics);

// GET - Get FAQs by category
router.get("/faqs/category/:category", getFAQsByCategory);

// GET - Get FAQ by ID
router.get("/faqs/:id", getFAQById);

// PUT - Update FAQ by ID
router.put("/faqs/:id", updateFAQ);

// DELETE - Delete FAQ by ID
router.delete("/faqs/:id", deleteFAQ);

module.exports = router;
