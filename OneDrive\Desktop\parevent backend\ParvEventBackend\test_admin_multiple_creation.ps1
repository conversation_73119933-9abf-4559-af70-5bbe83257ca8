$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTYxMzk3LCJleHAiOjE3NTExNjQ5OTd9.QEYGEFai2070jIVyqtSL26e40KGUNNyropKf9nR-U_c'
}

Write-Host "🎯 Testing Admin Multiple Creation Capabilities"
Write-Host "=============================================="

# Test Multiple Review Creation
Write-Host "`n📝 Testing Multiple Review Creation..."
$reviewCount = 0
for ($i = 1; $i -le 3; $i++) {
    $reviewData = @{
        name = "Test User $i"
        review = "This is test review number $i for admin testing"
        star = (Get-Random -Minimum 3 -Maximum 6)
        relationship = "client"
        eventType = "wedding"
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData
        Write-Host "✅ Review $i created - ID: $($result.data._id)"
        $reviewCount++
    } catch {
        Write-Host "❌ Review $i failed: $($_.Exception.Message)"
    }
}
Write-Host "📊 Reviews created: $reviewCount/3"

# Test Multiple Team Member Creation (without image - should work for text-only)
Write-Host "`n👥 Testing Multiple Team Member Creation..."
$teamCount = 0
for ($i = 1; $i -le 3; $i++) {
    $teamData = @{
        name = "Team Member $i"
        sortOrder = $i
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/team' -Method POST -Headers $headers -ContentType 'application/json' -Body $teamData
        Write-Host "✅ Team Member $i created - ID: $($result.data._id)"
        $teamCount++
    } catch {
        Write-Host "❌ Team Member $i failed: $($_.Exception.Message)"
    }
}
Write-Host "📊 Team members created: $teamCount/3"

# Test Hero Section Creation (without image - should fail as expected)
Write-Host "`n🦸 Testing Hero Section Creation (without image - should fail)..."
$heroData = @{
    title = "Test Hero Section"
    subtitle = "Test Subtitle"
    description = "Test Description"
} | ConvertTo-Json

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/hero-section' -Method POST -Headers $headers -ContentType 'application/json' -Body $heroData
    Write-Host "❌ UNEXPECTED: Hero section created without image"
} catch {
    Write-Host "✅ EXPECTED: Hero section requires image - $($_.Exception.Message)"
}

# Test Gallery Creation (without image - should fail as expected)
Write-Host "`n🖼️ Testing Gallery Creation (without image - should fail)..."
$galleryData = @{
    title = "Test Gallery Item"
    description = "Test Description"
    keywords = @("test", "gallery")
    category = "wedding"
} | ConvertTo-Json

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/gallery' -Method POST -Headers $headers -ContentType 'application/json' -Body $galleryData
    Write-Host "❌ UNEXPECTED: Gallery item created without image"
} catch {
    Write-Host "✅ EXPECTED: Gallery requires image - $($_.Exception.Message)"
}

Write-Host "`n🏆 FINAL RESULTS:"
Write-Host "✅ Admin can create multiple reviews: $($reviewCount -gt 0)"
Write-Host "✅ Admin can create multiple team members: $($teamCount -gt 0)"
Write-Host "✅ Hero section properly requires image"
Write-Host "✅ Gallery properly requires image"
Write-Host "`nAdmin Multiple Creation Test Complete!"
