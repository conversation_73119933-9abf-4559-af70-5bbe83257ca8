const Venue = require('../models/venue.model');
const { StatusCodes } = require('http-status-codes');
const Joi = require('joi');

// Validation schemas
const venueSchema = Joi.object({
    name: Joi.string()
        .required()
        .max(200)
        .messages({
            'string.max': 'Name cannot exceed 200 characters',
            'any.required': 'Name is required'
        }),
    venueType: Joi.string()
        .required()
        .valid('banquet-hall', 'outdoor', 'resort', 'hotel', 'farmhouse', 'palace', 'garden', 'beach', 'other')
        .messages({
            'any.required': 'Venue type is required',
            'any.only': 'Please select a valid venue type'
        }),
    location: Joi.string()
        .required()
        .max(500)
        .messages({
            'string.max': 'Location cannot exceed 500 characters',
            'any.required': 'Location is required'
        }),
    capacity: Joi.number()
        .required()
        .integer()
        .min(1)
        .max(15000)
        .messages({
            'number.base': 'Capacity must be a number',
            'number.integer': 'Capacity must be an integer',
            'number.min': 'Capacity must be at least 1',
            'number.max': 'Capacity cannot exceed 15000',
            'any.required': 'Capacity is required'
        }),
    seats: Joi.number()
        .required()
        .integer()
        .min(1)
        .max(10000)
        .messages({
            'number.base': 'Seats must be a number',
            'number.integer': 'Seats must be an integer',
            'number.min': 'Seats must be at least 1',
            'number.max': 'Seats cannot exceed 10000',
            'any.required': 'Seats is required'
        }),
    sortOrder: Joi.number().integer().min(0).default(0)
});

const updateVenueSchema = Joi.object({
    name: Joi.string().max(200),
    venueType: Joi.string().valid('banquet-hall', 'outdoor', 'resort', 'hotel', 'farmhouse', 'palace', 'garden', 'beach', 'other'),
    location: Joi.string().max(500),
    capacity: Joi.number().integer().min(1).max(15000),
    seats: Joi.number().integer().min(1).max(10000),
    sortOrder: Joi.number().integer().min(0)
});

// Create Venue
const createVenue = async (req, res) => {
    try {
        console.log('🏛️ Creating new venue...');

        // Check if image is uploaded
        if (!req.file) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Image is required'
            });
        }

        // Validate request body
        const { error, value } = venueSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Create venue data
        const venueData = { ...value };
        venueData.image = req.file.location;

        const venue = new Venue(venueData);
        await venue.save();

        console.log('✅ Venue created:', venue._id);

        res.status(StatusCodes.CREATED).json({
            success: true,
            message: 'Venue created successfully',
            data: venue.getPublicData()
        });

    } catch (error) {
        console.error('❌ Create venue error:', error);
        
        // Allow admin to create multiple venues - no duplicate check needed

        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to create venue',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get All Venues
const getAllVenues = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = 'createdAt',
            sortOrder = 'desc',
            venueType,
            search
        } = req.query;

        console.log(`🔍 Fetching venues - Page: ${page}, Limit: ${limit}`);

        // Build filter
        const filter = { isActive: true };

        if (venueType) {
            filter.venueType = venueType;
        }

        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { location: { $regex: search, $options: 'i' } }
            ];
        }

        // Build sort
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

        // Execute query with pagination
        const venues = await Venue.find(filter)
            .sort(sort)
            .limit(parseInt(limit))
            .skip((parseInt(page) - 1) * parseInt(limit));

        const total = await Venue.countDocuments(filter);
        const totalPages = Math.ceil(total / parseInt(limit));

        console.log(`✅ Found ${venues.length} venues`);

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                venues: venues.map(venue => venue.getPublicData()),
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalItems: total,
                    itemsPerPage: parseInt(limit),
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1
                }
            }
        });

    } catch (error) {
        console.error('❌ Get all venues error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch venues',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get venue by ID
const getVenueById = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🔍 Fetching venue by ID:', id);

        // Find by MongoDB ObjectId only
        let venue = await Venue.findOne({
            _id: id,
            isActive: true
        });

        if (!venue) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Venue not found'
            });
        }

        console.log('✅ Venue found:', venue._id);

        res.status(StatusCodes.OK).json({
            success: true,
            data: venue.getPublicData()
        });

    } catch (error) {
        console.error('❌ Get venue by ID error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch venue',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Update Venue
const updateVenue = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🔄 Updating venue:', id);

        // Find venue by ObjectId
        let venue = await Venue.findOne({
            _id: id,
            isActive: true
        });

        if (!venue) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Venue not found'
            });
        }

        // Validate request body
        const { error, value } = updateVenueSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Update venue fields
        Object.keys(value).forEach(key => {
            venue[key] = value[key];
        });

        // Update image if new one is uploaded
        if (req.file) {
            venue.image = req.file.location;
        }

        await venue.save();

        console.log('✅ Venue updated:', venue._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Venue updated successfully',
            data: venue.getPublicData()
        });

    } catch (error) {
        console.error('❌ Update venue error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to update venue',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Delete Venue
const deleteVenue = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🗑️ Deleting venue:', id);

        // Find and soft delete venue
        const venue = await Venue.findOneAndUpdate(
            { _id: id, isActive: true },
            { isActive: false },
            { new: true }
        );

        if (!venue) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Venue not found'
            });
        }

        console.log('✅ Venue deleted:', venue._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Venue deleted successfully',
            data: {
                _id: venue._id,
                name: venue.name,
                isActive: venue.isActive
            }
        });

    } catch (error) {
        console.error('❌ Delete venue error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to delete venue',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

module.exports = {
    createVenue,
    getAllVenues,
    getVenueById,
    updateVenue,
    deleteVenue
};
