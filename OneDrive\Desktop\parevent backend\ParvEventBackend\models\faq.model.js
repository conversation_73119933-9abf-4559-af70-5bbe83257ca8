const mongoose = require("mongoose");
const AutoIncrement = require("mongoose-sequence")(mongoose);

const faqSchema = new mongoose.Schema(
  {
    id: { type: Number, unique: true },
    question: {
      type: String,
      required: [true, "Question is required"],
      trim: true,
      maxlength: [500, "Question cannot exceed 500 characters"]
    },
    answer: {
      type: String,
      required: [true, "Answer is required"],
      trim: true,
      maxlength: [2000, "Answer cannot exceed 2000 characters"]
    },
    category: {
      type: String,
      trim: true,
      maxlength: [100, "Category cannot exceed 100 characters"],
      default: null
    },
    status: {
      type: String,
      enum: {
        values: ["active", "inactive"],
        message: "Status must be either active or inactive"
      },
      default: "active"
    },
    order: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true
  }
);

// Index for better search performance
faqSchema.index({ question: 'text', answer: 'text', category: 'text' });
faqSchema.index({ category: 1 });
faqSchema.index({ status: 1 });
faqSchema.index({ order: 1 });
faqSchema.index({ createdAt: -1 });

// Compound indexes for common queries
faqSchema.index({ status: 1, order: 1 });
faqSchema.index({ category: 1, status: 1 });

faqSchema.plugin(AutoIncrement, { inc_field: "id", id: "faq_id_counter" });

module.exports = mongoose.model("FAQ", faqSchema);
