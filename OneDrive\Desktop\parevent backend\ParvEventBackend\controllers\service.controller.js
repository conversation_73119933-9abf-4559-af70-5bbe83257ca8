const Service = require('../models/service.model');
const { StatusCodes } = require('http-status-codes');
const Joi = require('joi');

// Validation schemas
const serviceSchema = Joi.object({
    title: Joi.string()
        .required()
        .max(200)
        .messages({
            'string.max': 'Title cannot exceed 200 characters',
            'any.required': 'Title is required'
        }),
    description: Joi.string()
        .required()
        .max(1000)
        .messages({
            'string.max': 'Description cannot exceed 1000 characters',
            'any.required': 'Description is required'
        }),
    description2: Joi.string()
        .max(1000)
        .allow('')
        .messages({
            'string.max': 'Description 2 cannot exceed 1000 characters'
        }),
    howWeDoIt: Joi.array().items(
        Joi.object({
            title: Joi.string()
                .max(200)
                .allow('')
                .messages({
                    'string.max': 'How we do it title cannot exceed 200 characters'
                }),
            description: Joi.string()
                .max(2000)
                .allow('')
                .messages({
                    'string.max': 'How we do it description cannot exceed 2000 characters'
                })
        })
    ).optional(),
    sortOrder: Joi.number().integer().min(0).default(0)
});

const updateServiceSchema = Joi.object({
    title: Joi.string().max(200),
    description: Joi.string().max(1000),
    description2: Joi.string().max(1000).allow(''),
    howWeDoIt: Joi.array().items(
        Joi.object({
            title: Joi.string().max(200).allow(''),
            description: Joi.string().max(2000).allow('')
        })
    ).optional(),
    sortOrder: Joi.number().integer().min(0)
});

// Create Service
const createService = async (req, res) => {
    try {
        console.log('🛠️ Creating new service...');

        // Enhanced file validation
        if (!req.files) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'No files uploaded'
            });
        }

        const { icons, image } = req.files;
        
        // Validate both files exist and have at least one file
        if (!icons || !Array.isArray(icons) || icons.length === 0) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Service icons (PNG/JPG) are required'
            });
        }

        if (!image || !Array.isArray(image) || image.length === 0) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Service image is required'
            });
        }

        // Validate request body
        const { error, value } = serviceSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Create service data - use first file from each array
        const serviceData = { 
            ...value,
            icons: icons[0].location,  // Use first uploaded icon
            image: image[0].location    // Use first uploaded image
        };

        const service = new Service(serviceData);
        await service.save();

        console.log('✅ Service created:', service._id);

        return res.status(StatusCodes.CREATED).json({
            success: true,
            message: 'Service created successfully',
            data: service.getPublicData()
        });

    } catch (error) {
        console.error('❌ Create service error:', error);

        // Handle duplicate key error specifically
        if (error.code === 11000) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Service creation failed due to duplicate data. Please try again.',
                ...(process.env.NODE_ENV === 'development' && {
                    debug: 'Duplicate key error - this may be due to an old database index. Please contact administrator to fix database indexes.',
                    error: error.message
                })
            });
        }

        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to create service',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get All Services
const getAllServices = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = 'createdAt',
            sortOrder = 'desc',
            search
        } = req.query;

        console.log(`🔍 Fetching services - Page: ${page}, Limit: ${limit}`);

        // Build filter
        const filter = { isActive: true };

        if (search) {
            filter.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { description2: { $regex: search, $options: 'i' } }
            ];
        }

        // Build sort
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

        // Execute query with pagination
        const services = await Service.find(filter)
            .sort(sort)
            .limit(parseInt(limit))
            .skip((parseInt(page) - 1) * parseInt(limit));

        const total = await Service.countDocuments(filter);
        const totalPages = Math.ceil(total / parseInt(limit));

        console.log(`✅ Found ${services.length} services`);

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                services: services.map(service => service.getPublicData()),
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalItems: total,
                    itemsPerPage: parseInt(limit),
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1
                }
            }
        });

    } catch (error) {
        console.error('❌ Get all services error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch services',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get Service by ID
const getServiceById = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🔍 Fetching service by ID:', id);

        // Find by MongoDB ObjectId only
        let service = await Service.findOne({
            _id: id,
            isActive: true
        });

        if (!service) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Service not found'
            });
        }

        console.log('✅ Service found:', service._id);

        res.status(StatusCodes.OK).json({
            success: true,
            data: service.getPublicData()
        });

    } catch (error) {
        console.error('❌ Get service by ID error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch service',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Update Service
const updateService = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🔄 Updating service:', id);

        // Find service by ObjectId
        let service = await Service.findOne({
            _id: id,
            isActive: true
        });

        if (!service) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Service not found'
            });
        }

        // Validate request body
        const { error, value } = updateServiceSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Update service fields
        Object.keys(value).forEach(key => {
            service[key] = value[key];
        });

        // Update files if new ones are uploaded
        if (req.files) {
            if (req.files.icons) {
                service.icons = req.files.icons[0].location; // PNG/JPG icon
            }
            if (req.files.image) {
                service.image = req.files.image[0].location;
            }
        }

        await service.save();

        console.log('✅ Service updated:', service._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Service updated successfully',
            data: service.getPublicData()
        });

    } catch (error) {
        console.error('❌ Update service error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to update service',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Delete Service
const deleteService = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🗑️ Deleting service:', id);

        // Find and soft delete service
        const service = await Service.findOneAndUpdate(
            { _id: id, isActive: true },
            { isActive: false },
            { new: true }
        );

        if (!service) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Service not found'
            });
        }

        console.log('✅ Service deleted:', service._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Service deleted successfully',
            data: {
                _id: service._id,
                title: service.title,
                isActive: service.isActive
            }
        });

    } catch (error) {
        console.error('❌ Delete service error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to delete service',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

module.exports = {
    createService,
    getAllServices,
    getServiceById,
    updateService,
    deleteService
};
