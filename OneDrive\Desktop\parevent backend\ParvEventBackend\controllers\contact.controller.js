const contactService = require("../services/contact.service");

// Create new contact
const createContact = async (req, res) => {
  try {
    const contactData = req.body;
    const result = await contactService.createContact(contactData);

    res.status(201).json({
      status: true,
      code: 201,
      message: "Contact created successfully",
      data: result
    });
  } catch (error) {
    console.error("Error creating contact:", error);
    res.status(400).json({
      status: false,
      code: 400,
      message: error.message || "Failed to create contact",
      data: null
    });
  }
};

// Get all contacts with filtering and pagination
const getAllContacts = async (req, res) => {
  try {
    const filters = {
      page: req.query.page,
      limit: req.query.limit,
      status: req.query.status,
      service: req.query.service,
      priority: req.query.priority,
      search: req.query.search,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await contactService.getAllContacts(filters);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Contacts retrieved successfully",
      data: result.contacts,
      pagination: result.pagination
    });
  } catch (error) {
    console.error("Error getting contacts:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to retrieve contacts",
      data: null
    });
  }
};

// Get contact by ID
const getContactById = async (req, res) => {
  try {
    const contactId = req.params.id;
    const contact = await contactService.getContactById(contactId);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Contact retrieved successfully",
      data: contact
    });
  } catch (error) {
    console.error("Error getting contact by ID:", error);
    const statusCode = error.message === "Contact not found" ? 404 : 400;
    res.status(statusCode).json({
      status: false,
      code: statusCode,
      message: error.message || "Failed to retrieve contact",
      data: null
    });
  }
};

// Update contact
const updateContact = async (req, res) => {
  try {
    const contactId = req.params.id;
    const updateData = req.body;
    
    const updatedContact = await contactService.updateContact(contactId, updateData);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Contact updated successfully",
      data: updatedContact
    });
  } catch (error) {
    console.error("Error updating contact:", error);
    const statusCode = error.message === "Contact not found" ? 404 : 400;
    res.status(statusCode).json({
      status: false,
      code: statusCode,
      message: error.message || "Failed to update contact",
      data: null
    });
  }
};

// Delete contact
const deleteContact = async (req, res) => {
  try {
    const contactId = req.params.id;
    const deletedContact = await contactService.deleteContact(contactId);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Contact deleted successfully",
      data: deletedContact
    });
  } catch (error) {
    console.error("Error deleting contact:", error);
    const statusCode = error.message === "Contact not found" ? 404 : 400;
    res.status(statusCode).json({
      status: false,
      code: statusCode,
      message: error.message || "Failed to delete contact",
      data: null
    });
  }
};

// Get contact statistics
const getContactStatistics = async (req, res) => {
  try {
    const stats = await contactService.getContactStatistics();

    res.status(200).json({
      status: true,
      code: 200,
      message: "Contact statistics retrieved successfully",
      data: stats
    });
  } catch (error) {
    console.error("Error getting contact statistics:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to retrieve contact statistics",
      data: null
    });
  }
};

// Get contacts by status
const getContactsByStatus = async (req, res) => {
  try {
    const status = req.params.status;
    const filters = {
      status,
      page: req.query.page,
      limit: req.query.limit,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await contactService.getAllContacts(filters);

    res.status(200).json({
      status: true,
      code: 200,
      message: `Contacts with status '${status}' retrieved successfully`,
      data: result.contacts,
      pagination: result.pagination
    });
  } catch (error) {
    console.error("Error getting contacts by status:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to retrieve contacts by status",
      data: null
    });
  }
};

// Get contacts by service
const getContactsByService = async (req, res) => {
  try {
    const service = req.params.service;
    const filters = {
      service,
      page: req.query.page,
      limit: req.query.limit,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await contactService.getAllContacts(filters);

    res.status(200).json({
      status: true,
      code: 200,
      message: `Contacts for service '${service}' retrieved successfully`,
      data: result.contacts,
      pagination: result.pagination
    });
  } catch (error) {
    console.error("Error getting contacts by service:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to retrieve contacts by service",
      data: null
    });
  }
};

module.exports = {
  createContact,
  getAllContacts,
  getContactById,
  updateContact,
  deleteContact,
  getContactStatistics,
  getContactsByStatus,
  getContactsByService
};
