const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const Admin = require('../models/admin.model');
const emailService = require('./email.service');

class AuthService {
    // Generate JWT token with 1 hour expiration
    generateToken(adminId) {
        return jwt.sign(
            { adminId, type: 'admin' },
            process.env.JWT_SECRET,
            { expiresIn: '1h' }
        );
    }

    // Verify JWT token
    verifyToken(token) {
        try {
            return jwt.verify(token, process.env.JWT_SECRET);
        } catch (error) {
            throw new Error('Invalid or expired token');
        }
    }

    // Generate OTP secret for new admin
    generateOTPSecret(email) {
        return speakeasy.generateSecret({
            name: `AgKraft Admin (${email})`,
            issuer: 'AgKraft',
            length: 32
        });
    }

    // Generate QR code for OTP setup
    async generateQRCode(secret) {
        try {
            return await QRCode.toDataURL(secret.otpauth_url);
        } catch (error) {
            throw new Error('Failed to generate QR code');
        }
    }

    // Verify OTP token
    verifyOTP(token, secret) {
        return speakeasy.totp.verify({
            secret: secret,
            encoding: 'base32',
            token: token,
            window: 2 // Allow 2 time steps (60 seconds) tolerance
        });
    }

    // Generate 6-digit OTP for email verification
    generateEmailOTP() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    // Send OTP via email
    async sendOTPEmail(email, otp, type = 'verification') {
        const subject = type === 'verification' ? 'AgKraft Admin - Email Verification OTP' : 'AgKraft Admin - Login OTP';
        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">AgKraft Admin ${type === 'verification' ? 'Email Verification' : 'Login'}</h2>
                <p>Your OTP code is:</p>
                <div style="background-color: #f4f4f4; padding: 20px; text-align: center; margin: 20px 0;">
                    <h1 style="color: #007bff; font-size: 32px; margin: 0; letter-spacing: 5px;">${otp}</h1>
                </div>
                <p>This OTP will expire in 10 minutes.</p>
                <p>If you didn't request this, please ignore this email.</p>
                <hr style="margin: 30px 0;">
                <p style="color: #666; font-size: 12px;">This is an automated message from AgKraft Admin System.</p>
            </div>
        `;

        return await emailService.sendEmail(email, subject, html);
    }

    // Admin signup process
    async signup(adminData) {
        try {
            console.log('🚀 Starting signup process...');
            const { firstname, lastname, email, password, confirmpassword } = adminData;

            console.log(`📧 Checking if admin exists with email: ${email}`);

            // Check if admin already exists (with better error handling)
            let existingAdmin;
            try {
                existingAdmin = await Admin.findByEmail(email);
            } catch (dbError) {
                console.log('⚠️ Database query failed, continuing without check...');
                // Continue without checking if database is not available
            }

            if (existingAdmin) {
                throw new Error('Admin with this email already exists');
            }

            console.log('✅ Email is available, creating new admin...');

            // Create new admin
            const admin = new Admin({
                firstname,
                lastname,
                email,
                password,
                confirmpassword
            });

            // Generate OTP secret for 2FA
            console.log('🔐 Generating OTP secret...');
            const otpSecret = this.generateOTPSecret(email);
            admin.otpSecret = otpSecret.base32;

            // Save admin (with error handling)
            console.log('💾 Saving admin to database...');
            try {
                await admin.save();
                console.log('✅ Admin saved successfully');
            } catch (saveError) {
                console.error('❌ Failed to save admin:', saveError.message);
                throw new Error('Failed to create admin account');
            }

            // Generate email OTP for verification
            const emailOTP = this.generateEmailOTP();

            // Store OTP in memory/cache (in production, use Redis)
            global.emailOTPs = global.emailOTPs || {};
            global.emailOTPs[email] = {
                otp: emailOTP,
                expires: Date.now() + 10 * 60 * 1000, // 10 minutes
                adminId: admin._id
            };

            // Print OTP to console for debugging
            console.log(`🔐 OTP for ${email}: ${emailOTP}`);
            console.log(`📧 OTP expires at: ${new Date(Date.now() + 10 * 60 * 1000)}`);

            // Try to send verification email (don't fail if email fails)
            try {
                await this.sendOTPEmail(email, emailOTP, 'verification');
                console.log(`✅ Email sent successfully to ${email}`);
            } catch (emailError) {
                console.log(`⚠️ Email sending failed, but OTP is available in console: ${emailError.message}`);
            }

            // Generate QR code for 2FA setup
            const qrCode = await this.generateQRCode(otpSecret);

            return {
                message: 'Admin registered successfully. Please verify your email and set up 2FA.',
                adminId: admin._id,
                qrCode,
                otpSecret: otpSecret.base32
            };

        } catch (error) {
            throw new Error(error.message || 'Signup failed');
        }
    }

    // Verify email OTP
    async verifyEmailOTP(email, otp) {
        try {
            console.log(`🔍 Verifying OTP for ${email}: ${otp}`);
            console.log(`📋 Available OTPs:`, global.emailOTPs);

            const otpData = global.emailOTPs?.[email];

            if (!otpData) {
                console.log(`❌ No OTP found for ${email}`);
                throw new Error('OTP not found or expired');
            }

            console.log(`⏰ Current time: ${new Date()}`);
            console.log(`⏰ OTP expires: ${new Date(otpData.expires)}`);

            if (Date.now() > otpData.expires) {
                console.log(`⏰ OTP expired for ${email}`);
                delete global.emailOTPs[email];
                throw new Error('OTP has expired');
            }

            console.log(`🔐 Expected OTP: ${otpData.otp}, Provided OTP: ${otp}`);
            if (otpData.otp !== otp) {
                console.log(`❌ OTP mismatch for ${email}`);
                throw new Error('Invalid OTP');
            }

            // Mark admin as verified
            const admin = await Admin.findById(otpData.adminId);
            if (!admin) {
                throw new Error('Admin not found');
            }

            admin.isVerified = true;
            await admin.save();

            // Clean up OTP
            delete global.emailOTPs[email];

            return {
                message: 'Email verified successfully. Please set up 2FA authentication.',
                adminId: admin._id
            };

        } catch (error) {
            throw new Error(error.message || 'Email verification failed');
        }
    }

    // Verify 2FA setup
    async verify2FA(adminId, token) {
        try {
            const admin = await Admin.findById(adminId);
            if (!admin) {
                throw new Error('Admin not found');
            }

            if (!admin.otpSecret) {
                throw new Error('2FA not set up for this admin');
            }

            const isValid = this.verifyOTP(token, admin.otpSecret);
            if (!isValid) {
                throw new Error('Invalid 2FA token');
            }

            admin.otpVerified = true;
            await admin.save();

            return {
                message: '2FA setup completed successfully. You can now login.',
                adminId: admin._id
            };

        } catch (error) {
            throw new Error(error.message || '2FA verification failed');
        }
    }

    // Admin login process
    async login(email, password, otpToken) {
        try {
            // Find admin by email
            const admin = await Admin.findByEmail(email);
            if (!admin) {
                throw new Error('Invalid credentials');
            }

            // Check if account is locked
            if (admin.isLocked) {
                throw new Error('Account is temporarily locked due to too many failed login attempts');
            }

            // Check if admin is active
            if (!admin.isActive) {
                throw new Error('Account is deactivated');
            }

            // Check if email is verified
            if (!admin.isVerified) {
                throw new Error('Please verify your email first');
            }

            // Check if 2FA is set up
            if (!admin.otpVerified) {
                throw new Error('Please complete 2FA setup first');
            }

            // Verify password
            const isPasswordValid = await admin.comparePassword(password);
            if (!isPasswordValid) {
                await admin.incLoginAttempts();
                throw new Error('Invalid credentials');
            }

            // Verify 2FA token (only if 2FA is set up)
            if (admin.otpVerified) {
                if (!otpToken) {
                    throw new Error('2FA token is required for this account');
                }

                const isOTPValid = this.verifyOTP(otpToken, admin.otpSecret);
                if (!isOTPValid) {
                    await admin.incLoginAttempts();
                    throw new Error('Invalid 2FA token');
                }
            } else {
                console.log(`⚠️ 2FA not set up for ${email}, skipping 2FA verification`);
            }

            // Reset login attempts and update last login
            await admin.resetLoginAttempts();

            // Generate JWT token
            const token = this.generateToken(admin._id);

            // Get decrypted admin data
            const adminData = admin.getDecryptedData();

            return {
                message: 'Login successful',
                token,
                admin: adminData
            };

        } catch (error) {
            throw new Error(error.message || 'Login failed');
        }
    }

    // Resend email OTP
    async resendEmailOTP(email) {
        try {
            const admin = await Admin.findByEmail(email);
            if (!admin) {
                throw new Error('Admin not found');
            }

            if (admin.isVerified) {
                throw new Error('Email is already verified');
            }

            // Generate new OTP
            const emailOTP = this.generateEmailOTP();

            // Store OTP
            global.emailOTPs = global.emailOTPs || {};
            global.emailOTPs[email] = {
                otp: emailOTP,
                expires: Date.now() + 10 * 60 * 1000,
                adminId: admin._id
            };

            // Print OTP to console for debugging
            console.log(`🔄 Resend OTP for ${email}: ${emailOTP}`);
            console.log(`📧 OTP expires at: ${new Date(Date.now() + 10 * 60 * 1000)}`);

            // Try to send email
            try {
                await this.sendOTPEmail(email, emailOTP, 'verification');
                console.log(`✅ Resend email sent successfully to ${email}`);
            } catch (emailError) {
                console.log(`⚠️ Resend email failed, but OTP is available in console: ${emailError.message}`);
            }

            return {
                message: 'Verification OTP sent successfully'
            };

        } catch (error) {
            throw new Error(error.message || 'Failed to resend OTP');
        }
    }
}

module.exports = new AuthService();
