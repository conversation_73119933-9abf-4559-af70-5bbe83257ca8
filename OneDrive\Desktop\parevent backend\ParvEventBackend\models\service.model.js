const mongoose = require('mongoose');

const serviceSchema = new mongoose.Schema({
    title: {
        type: String,
        required: [true, 'Title is required'],
        trim: true,
        maxlength: [200, 'Title cannot exceed 200 characters']
    },
    description: {
        type: String,
        required: [true, 'Description is required'],
        trim: true,
        maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    description2: {
        type: String,
        trim: true,
        maxlength: [1000, 'Description 2 cannot exceed 1000 characters']
    },
    icons: {
        type: String,
        required: [true, 'Service icon is required'] // Now required - PNG/JPG icons
    },
    image: {
        type: String,
        required: [true, 'Service image is required']
    },
    howWeDoIt: [{
        title: {
            type: String,
            trim: true,
            maxlength: [200, 'How we do it title cannot exceed 200 characters']
        },
        description: {
            type: String,
            trim: true,
            maxlength: [2000, 'How we do it description cannot exceed 2000 characters']
        }
    }],
    isActive: {
        type: Boolean,
        default: true
    },
    sortOrder: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});

// Index for better performance
serviceSchema.index({ title: 'text', description: 'text', description2: 'text' });
serviceSchema.index({ isActive: 1 });
serviceSchema.index({ sortOrder: 1 });

// Method to get public data
serviceSchema.methods.getPublicData = function() {
    return {
        _id: this._id,
        title: this.title,
        description: this.description,
        description2: this.description2,
        icons: this.icons, // Now PNG/JPG icons
        image: this.image,
        howWeDoIt: this.howWeDoIt,
        isActive: this.isActive,
        sortOrder: this.sortOrder,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt
    };
};

module.exports = mongoose.model('Service', serviceSchema);
