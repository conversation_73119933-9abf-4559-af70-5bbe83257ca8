const express = require('express');
const router = express.Router();
const {
    createHeroSection,
    getAllHeroSections,
    getHeroSectionById,
    updateHeroSection,
    deleteHeroSection,
    getPrimaryHeroSection
} = require('../controllers/heroSection.controller');
const { heroUploadSingle } = require('../config/awsclouds3');
const authMiddleware = require('../middleware/auth.middleware');

// Apply security headers to all hero section routes
router.use(authMiddleware.securityHeaders);

// Public routes (no authentication required)
router.get('/', getAllHeroSections);
router.get('/primary', getPrimaryHeroSection);
router.get('/:id', getHeroSectionById);

// Protected routes (authentication required)
router.post('/', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    heroUploadSingle('image'),
    createHeroSection
);

router.put('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    heroUploadSingle('image'),
    updateHeroSection
);

router.delete('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    deleteHeroSection
);

module.exports = router;
