$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTYxMzk3LCJleHAiOjE3NTExNjQ5OTd9.QEYGEFai2070jIVyqtSL26e40KGUNNyropKf9nR-U_c'
}

Write-Host "Testing Single Review Creation"

$reviewData = @{
    name = "Test User"
    review = "Test review"
    star = 5
    relationship = "client"
    eventType = "wedding"
} | ConvertTo-Json

Write-Host "Review data: $reviewData"

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData
    Write-Host "SUCCESS: Review created"
    Write-Host "Response: $($result | ConvertTo-Json -Depth 3)"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    Write-Host "Full error: $($_.Exception)"
}
