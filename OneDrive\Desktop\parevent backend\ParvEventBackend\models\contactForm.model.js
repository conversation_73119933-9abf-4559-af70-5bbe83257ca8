const mongoose = require("mongoose");
const validator = require("validator");

const FormSchema = new mongoose.Schema(
  {
    first_name: {
      type: String,
      required: [true, "First name is required"],
      trim: true,
    },
    last_name: {
      type: String,
      required: [true, "Last name is required"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      validate: {
        validator: (value) => validator.isEmail(value),
        message: "Invalid email format",
      },
    },
    phone: {
      type: String,
      required: [true, "Phone number is required"],
      validate: {
        validator: (value) => validator.isMobilePhone(value, "en-IN"),
        message: "Invalid phone number",
      },
    },
    country: {
      type: String,
      required: [true, "Country is required"],
      trim: true,
    },
    city: {
      type: String,
      required: [true, "City is required"],
      trim: true,
    },
    message: {
      type: String,
    },
    services: {
      type: String,
      required: [true, "Service is required"],
    },
  },
  { 
    timestamps: true,
    toJSON: {
      transform: (doc, ret) => {
        // Convert timestamps to IST
        ret.createdAt = new Date(ret.createdAt).toLocaleString("en-IN", { timeZone: "Asia/Kolkata" });
        ret.updatedAt = new Date(ret.updatedAt).toLocaleString("en-IN", { timeZone: "Asia/Kolkata" });
        return ret;
      }
    }
  }
);

module.exports = mongoose.model("Form", FormSchema);
