Write-Host "Testing Service Creation - Simple Test"
Write-Host "======================================"

# Get fresh token
$loginData = '{"email":"<EMAIL>","password":"password123"}'

try {
    $loginResult = Invoke-RestMethod -Uri 'http://localhost:8005/api/auth/login-quick' -Method POST -ContentType 'application/json' -Body $loginData
    $token = $loginResult.data.token
    Write-Host "SUCCESS: Token obtained"
    
    $headers = @{
        'Authorization' = "Bearer $token"
    }
    
    # Test with minimal data (will fail due to missing files, but should show field validation)
    Write-Host "`nTesting field validation..."
    
    $testData = @{
        title = "Test Service"
        description = "Test description"
    } | ConvertTo-Json
    
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/services' -Method POST -Headers $headers -ContentType 'application/json' -Body $testData
    Write-Host "UNEXPECTED: Service created without files"
    
} catch {
    $errorMessage = $_.Exception.Message
    Write-Host "Error: $errorMessage"
    
    if ($errorMessage -like "*SVG icon and service image are required*") {
        Write-Host "✅ SUCCESS: Field validation working - files required"
    } elseif ($errorMessage -like "*Invalid field name*") {
        Write-Host "❌ STILL FIELD ERROR: Field name issue persists"
    } elseif ($errorMessage -like "*400*") {
        Write-Host "✅ PROGRESS: 400 error (validation working)"
    } else {
        Write-Host "❌ OTHER ERROR: $errorMessage"
    }
}

Write-Host "`nTest Complete!"
