# 🛠️ Service & Gallery APIs - Complete Documentation

## Overview
This documentation covers the Service Management and Gallery Management APIs, focusing on the public endpoints for retrieving service and gallery data for website integration.

## 🏗️ System Architecture

### Service Management System
- **Model**: `models/service.model.js` - Service data structure
- **Controller**: `controllers/service.controller.js` - Request handling
- **Routes**: `routes/service.route.js` - API endpoints
- **Base URL**: `/services`

### Gallery Management System
- **Model**: `models/gallery.model.js` - Gallery data structure
- **Controller**: `controllers/gallery.controller.js` - Request handling
- **Routes**: `routes/gallery.route.js` - API endpoints
- **Base URL**: `/gallery`

## 📊 Database Schemas

### Service Model
```javascript
{
  _id: ObjectId,
  title: String (required, max 200 chars),
  description: String (required, max 1000 chars),
  description2: String (optional, max 1000 chars),
  icons: String (required, PNG/JPG icon S3 URL - stored in services/icons/ folder),
  image: String (required, service image S3 URL - stored in services/images/ folder),
  howWeDoIt: [
    {
      title: String (max 200 chars),
      description: String (max 2000 chars)
    }
  ],
  isActive: Boolean (default: true),
  sortOrder: Number (default: 0),
  createdAt: Date,
  updatedAt: Date
}
```

### Icon System Configuration
#### PNG/JPG Icons
- **Field**: `icons`
- **File Type**: PNG, JPG, JPEG, GIF, WebP
- **Storage Location**: `services/icons/` folder in S3 bucket
- **File Size Limit**: 10MB maximum
- **MIME Types**: `image/jpeg`, `image/png`, `image/gif`, `image/webp`
- **Benefits**: Always displays properly, no browser compatibility issues

### Gallery Model
```javascript
{
  _id: ObjectId,
  title: String (required, max 100 chars),
  description: String (optional, max 500 chars),
  image: String (required, gallery image S3 URL),
  keywords: [String] (lowercase, trimmed),
  category: String (enum: wedding, corporate, birthday, anniversary, other),
  isActive: Boolean (default: true),
  sortOrder: Number (default: 0),
  createdAt: Date,
  updatedAt: Date
}
```

## 🔗 Service API Endpoints

### 1. Get All Services (Public)
```http
GET /services?page=1&limit=10&search=wedding&sortBy=sortOrder&sortOrder=asc
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search in title, description, description2
- `sortBy`: Sort field (default: createdAt)
- `sortOrder`: asc/desc (default: desc)

**Response:**
```javascript
{
  "success": true,
  "data": {
    "services": [
      {
        "_id": "ObjectId",
        "title": "String",
        "description": "String",
        "description2": "String",
        "icons": "String (PNG/JPG icon S3 URL)",
        "image": "String (S3 URL)",
        "howWeDoIt": [
          {
            "title": "String",
            "description": "String"
          }
        ],
        "isActive": true,
        "sortOrder": "Number",
        "createdAt": "Date",
        "updatedAt": "Date"
      }
    ],
    "pagination": {
      "currentPage": "Number",
      "totalPages": "Number",
      "totalItems": "Number",
      "itemsPerPage": "Number",
      "hasNextPage": "Boolean",
      "hasPrevPage": "Boolean"
    }
  }
}
```

### 2. Get Service by ID (Public)
```http
GET /services/:id
```

**Parameters:**
- `id`: Service ObjectId

**Response:**
```javascript
{
  "success": true,
  "data": {
    "_id": "ObjectId",
    "title": "String",
    "description": "String",
    "description2": "String",
    "icons": "String (PNG/JPG icon S3 URL)",
    "image": "String (S3 URL)",
    "howWeDoIt": [
      {
        "title": "String",
        "description": "String"
      }
    ],
    "isActive": true,
    "sortOrder": "Number",
    "createdAt": "Date",
    "updatedAt": "Date"
  }
}
```

## 🔗 Gallery API Endpoints

### 3. Get All Gallery Items (Public)
```http
GET /gallery?page=1&limit=10&category=wedding&keyword=decoration&sortBy=sortOrder&sortOrder=asc
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `category`: Filter by category (wedding, corporate, birthday, anniversary, other)
- `keyword`: Filter by keyword
- `sortBy`: Sort field (default: sortOrder)
- `sortOrder`: asc/desc (default: asc)

**Response:**
```javascript
{
  "success": true,
  "data": {
    "galleries": [
      {
        "_id": "ObjectId",
        "title": "String",
        "description": "String",
        "image": "String (S3 URL)",
        "keywords": ["String"],
        "category": "String",
        "sortOrder": "Number",
        "createdAt": "Date",
        "updatedAt": "Date"
      }
    ],
    "pagination": {
      "currentPage": "Number",
      "totalPages": "Number",
      "totalItems": "Number",
      "itemsPerPage": "Number",
      "hasNextPage": "Boolean",
      "hasPrevPage": "Boolean"
    }
  }
}
```

### 4. Get Gallery Item by ID (Public)
```http
GET /gallery/:id
```

**Parameters:**
- `id`: Gallery ObjectId

**Response:**
```javascript
{
  "success": true,
  "data": {
    "_id": "ObjectId",
    "title": "String",
    "description": "String",
    "image": "String (S3 URL)",
    "keywords": ["String"],
    "category": "String",
    "sortOrder": "Number",
    "createdAt": "Date",
    "updatedAt": "Date"
  }
}
```

## 🔍 Search & Filtering Features

### Service Search
- **Title Search**: Case-insensitive regex matching
- **Description Search**: Searches both description and description2 fields
- **Full-text Search**: Indexed search across multiple fields

### Gallery Filtering
- **Category Filter**: Filter by event type (wedding, corporate, etc.)
- **Keyword Filter**: Search within keywords array
- **Combined Filtering**: Multiple filters can be applied together

## 💡 Frontend Integration Examples

### Service Integration

#### Get All Services
```javascript
// Fetch all services with pagination
const getServices = async (page = 1, limit = 10) => {
  try {
    const response = await fetch(`/services?page=${page}&limit=${limit}&sortBy=sortOrder&sortOrder=asc`);
    const result = await response.json();
    
    if (result.success) {
      return result.data;
    }
    throw new Error(result.message);
  } catch (error) {
    console.error('Error fetching services:', error);
    return null;
  }
};

// Search services
const searchServices = async (searchTerm) => {
  try {
    const response = await fetch(`/services?search=${encodeURIComponent(searchTerm)}&limit=20`);
    const result = await response.json();
    
    if (result.success) {
      return result.data.services;
    }
    throw new Error(result.message);
  } catch (error) {
    console.error('Error searching services:', error);
    return [];
  }
};

// Get service details
const getServiceDetails = async (serviceId) => {
  try {
    const response = await fetch(`/services/${serviceId}`);
    const result = await response.json();
    
    if (result.success) {
      return result.data;
    }
    throw new Error(result.message);
  } catch (error) {
    console.error('Error fetching service details:', error);
    return null;
  }
};
```

#### Display Services on Website
```html
<!-- Services Section -->
<div id="services-container" class="row">
  <!-- Services will be loaded here -->
</div>

<!-- CSS for Service Icons -->
<style>
.service-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.service-card:hover .service-icon-img {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}
</style>

<script>
async function loadServices() {
  const servicesData = await getServices(1, 20);
  if (!servicesData) return;

  const container = document.getElementById('services-container');
  container.innerHTML = servicesData.services.map(service => `
    <div class="col-md-4 mb-4">
      <div class="card service-card h-100">
        <div class="card-body text-center">
          <div class="service-icon mb-3">
            <!-- Service icon -->
            <img src="${service.icons}"
                 alt="${service.title} Icon"
                 class="service-icon-img"
                 onerror="handleIconError(this, '${service.title}')">
          </div>
          <h5 class="card-title">${service.title}</h5>
          <p class="card-text">${service.description}</p>
          <button class="btn btn-primary" onclick="viewServiceDetails('${service._id}')">
            Learn More
          </button>
        </div>
      </div>
    </div>
  `).join('');
}

async function viewServiceDetails(serviceId) {
  const service = await getServiceDetails(serviceId);
  if (!service) return;

  // Display service details in modal or new page
  showServiceModal(service);
}

// Handle icon loading errors with fallback
function handleIconError(img, serviceTitle) {
  console.warn('Icon failed to load:', img.src);
  img.style.display = 'none';
  img.parentElement.innerHTML = `
    <div class="icon-fallback" style="
      background: #6c757d;
      color: white;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1.5rem;
    ">
      ${serviceTitle.charAt(0).toUpperCase()}
    </div>
  `;
}

// Load services when page loads
document.addEventListener('DOMContentLoaded', loadServices);
</script>

<!-- Icon error handling is included in the main script above -->
```

### Gallery Integration

#### Get Gallery Items
```javascript
// Fetch gallery items with filtering
const getGalleryItems = async (category = '', keyword = '', page = 1, limit = 12) => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sortBy: 'sortOrder',
      sortOrder: 'asc'
    });
    
    if (category) params.append('category', category);
    if (keyword) params.append('keyword', keyword);
    
    const response = await fetch(`/gallery?${params}`);
    const result = await response.json();
    
    if (result.success) {
      return result.data;
    }
    throw new Error(result.message);
  } catch (error) {
    console.error('Error fetching gallery:', error);
    return null;
  }
};

// Get gallery item details
const getGalleryItemDetails = async (galleryId) => {
  try {
    const response = await fetch(`/gallery/${galleryId}`);
    const result = await response.json();
    
    if (result.success) {
      return result.data;
    }
    throw new Error(result.message);
  } catch (error) {
    console.error('Error fetching gallery item:', error);
    return null;
  }
};
```

#### Display Gallery on Website
```html
<!-- Gallery Section -->
<div class="gallery-filters mb-4">
  <button class="btn btn-outline-primary" onclick="filterGallery('')">All</button>
  <button class="btn btn-outline-primary" onclick="filterGallery('wedding')">Wedding</button>
  <button class="btn btn-outline-primary" onclick="filterGallery('corporate')">Corporate</button>
  <button class="btn btn-outline-primary" onclick="filterGallery('birthday')">Birthday</button>
</div>

<div id="gallery-container" class="row">
  <!-- Gallery items will be loaded here -->
</div>

<script>
let currentCategory = '';

async function loadGallery(category = '') {
  currentCategory = category;
  const galleryData = await getGalleryItems(category, '', 1, 20);
  if (!galleryData) return;
  
  const container = document.getElementById('gallery-container');
  container.innerHTML = galleryData.galleries.map(item => `
    <div class="col-md-4 col-sm-6 mb-4">
      <div class="gallery-item">
        <img src="${item.image}" 
             alt="${item.title}" 
             class="img-fluid gallery-image"
             onclick="viewGalleryItem('${item._id}')">
        <div class="gallery-overlay">
          <h6>${item.title}</h6>
          <p class="small">${item.description || ''}</p>
          <div class="gallery-keywords">
            ${item.keywords.map(keyword => `<span class="badge bg-light text-dark">${keyword}</span>`).join(' ')}
          </div>
        </div>
      </div>
    </div>
  `).join('');
}

async function filterGallery(category) {
  // Update active filter button
  document.querySelectorAll('.gallery-filters .btn').forEach(btn => {
    btn.classList.remove('active');
  });
  event.target.classList.add('active');
  
  // Load filtered gallery
  await loadGallery(category);
}

async function viewGalleryItem(galleryId) {
  const item = await getGalleryItemDetails(galleryId);
  if (!item) return;
  
  // Display in lightbox or modal
  showGalleryModal(item);
}

// Load gallery when page loads
document.addEventListener('DOMContentLoaded', () => loadGallery());
</script>
```

## 🎨 Icon Usage

### PNG/JPG Icon Implementation
```html
<img src="${service.icons}" alt="${service.title}" class="service-icon">
```

### CSS Styling
```css
.service-icon {
  width: 60px;
  height: 60px;
  object-fit: contain;
}
```

## 🚀 Best Practices

### For Services
1. **Use sortOrder** for consistent service display order
2. **Implement search** for better user experience
3. **Display howWeDoIt** section for detailed service information
4. **Use SVG icons** for better scalability and performance
5. **Optimize SVG files** before upload (remove unnecessary metadata)
6. **Provide alt text** for accessibility

### For Gallery
1. **Implement category filtering** for better organization
2. **Use keyword search** for specific image finding
3. **Implement lazy loading** for better performance
4. **Add lightbox functionality** for image viewing

### Performance Optimization
1. **Use pagination** to limit data transfer
2. **Implement image optimization** for faster loading
3. **Cache frequently accessed data**
4. **Use appropriate sorting** for better user experience

## 🔧 Error Handling

### Common Error Responses
```javascript
// Not Found Error
{
  "success": false,
  "message": "Service/Gallery item not found"
}

// Server Error
{
  "success": false,
  "message": "Failed to fetch services/gallery items",
  "debug": "Error details (development only)"
}
```

## 📋 Response Structure

### Success Response
- `success`: Always true for successful requests
- `data`: Contains the requested data
- `pagination`: Included for list endpoints

### Error Response
- `success`: Always false for failed requests
- `message`: Human-readable error message
- `debug`: Technical error details (development only)

This documentation provides complete coverage of Service and Gallery APIs for easy website integration.
