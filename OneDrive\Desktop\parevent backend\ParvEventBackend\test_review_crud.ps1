$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTU1ODgxLCJleHAiOjE3NTExNTk0ODF9.GPbgqGvLiAH_0CeNZnkMcomH47jaTMAssmL4Zmhiokw'
}

Write-Host "=== Testing Review CRUD with ObjectId ==="

# Test 1: Create Review (without image)
Write-Host "`n1. Testing Review Creation..."
$reviewData = @{
    name = "Test User"
    review = "Great service! Highly recommended."
    star = 5
    relationship = "client"
    eventType = "wedding"
} | ConvertTo-Json

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData
    Write-Host "SUCCESS: Review created!"
    Write-Host "Review ID: $($result.data._id)"
    Write-Host "Name: $($result.data.name)"
    Write-Host "Rating: $($result.data.star) stars"
    $reviewId = $result.data._id
    
    # Test 2: Get Review by ObjectId
    Write-Host "`n2. Testing Get Review by ObjectId..."
    try {
        $getResult = Invoke-RestMethod -Uri "http://localhost:8005/api/reviews/$reviewId" -Headers $headers
        Write-Host "SUCCESS: Retrieved review with ObjectId"
        Write-Host "Name: $($getResult.data.name)"
        Write-Host "Review: $($getResult.data.review)"
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)"
    }
    
    # Test 3: Update Review
    Write-Host "`n3. Testing Update Review..."
    $updateData = @{
        name = "Updated Test User"
        review = "Updated review text - even better service!"
        star = 4
    } | ConvertTo-Json
    
    try {
        $updateResult = Invoke-RestMethod -Uri "http://localhost:8005/api/reviews/$reviewId" -Method PUT -Headers $headers -ContentType 'application/json' -Body $updateData
        Write-Host "SUCCESS: Review updated"
        Write-Host "New name: $($updateResult.data.name)"
        Write-Host "New rating: $($updateResult.data.star) stars"
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)"
    }
    
    # Test 4: Delete Review
    Write-Host "`n4. Testing Delete Review..."
    try {
        $deleteResult = Invoke-RestMethod -Uri "http://localhost:8005/api/reviews/$reviewId" -Method DELETE -Headers $headers
        Write-Host "SUCCESS: Review deleted"
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)"
    }
    
} catch {
    Write-Host "ERROR creating review: $($_.Exception.Message)"
}

# Test 5: Get All Reviews
Write-Host "`n5. Testing Get All Reviews..."
try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Headers $headers
    Write-Host "SUCCESS: Found $($result.data.reviews.Count) reviews"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}

Write-Host "`n=== Review CRUD Test Complete ==="
