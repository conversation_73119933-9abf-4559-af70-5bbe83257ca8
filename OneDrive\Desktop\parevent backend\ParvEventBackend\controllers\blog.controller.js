const blogservice = require("../services/blog.service");
const httpStatus = require("http-status-codes");
const mongoose = require("mongoose");

// Create Blogs
const createBlogPost = async (req, res) => {
  try {
    // Validate required fields
    const { title, description, category } = req.body;

    if (!title || !description || !category) {
      return res.status(400).json({
        status: false,
        code: 400,
        message: "Title, description, and category are required",
        data: null
      });
    }

    const result = await blogservice.createBlog(req.body, req.file);

    res.status(201).json({
      status: true,
      code: 201,
      message: "Blog created successfully",
      data: result
    });
  } catch (error) {
    console.error("Error creating blog:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to create blog",
      data: null
    });
  }
};

// Get Blogs
const getAllBlogs = async (req, res) => {
  try {
    const result = await blogservice.getAllBlogs(req.query);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Blogs retrieved successfully",
      data: result
    });
  } catch (error) {
    console.error("Error fetching blogs:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch blogs",
      data: null
    });
  }
};

const getBlogById = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: false,
        code: 400,
        message: "Invalid blog ID format",
        data: null
      });
    }

    const blog = await blogservice.getBlogById(id);
    if (!blog) {
      return res.status(404).json({
        status: false,
        code: 404,
        message: "Blog not found",
        data: null
      });
    }

    // Increment views when blog is accessed
    await blogservice.incrementBlogViews(id);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Blog retrieved successfully",
      data: blog
    });
  } catch (error) {
    console.error("Error fetching blog:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch blog",
      data: null
    });
  }
};

const updateBlog = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: false,
        code: 400,
        message: "Invalid blog ID format",
        data: null
      });
    }

    const blog = await blogservice.updateBlog(id, req.body, req.file);
    if (!blog) {
      return res.status(404).json({
        status: false,
        code: 404,
        message: "Blog not found",
        data: null
      });
    }

    res.status(200).json({
      status: true,
      code: 200,
      message: "Blog updated successfully",
      data: blog
    });
  } catch (error) {
    console.error("Error updating blog:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to update blog",
      data: null
    });
  }
};

const deleteBlogById = async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({
      status: false,
      code: 400,
      message: "Blog ID is required",
      data: null
    });
  }

  // Validate ObjectId format
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return res.status(400).json({
      status: false,
      code: 400,
      message: "Invalid blog ID format",
      data: null
    });
  }

  try {
    const blog = await blogservice.deleteBlog(id);
    if (!blog) {
      return res.status(404).json({
        status: false,
        code: 404,
        message: "Blog not found",
        data: null
      });
    }

    res.status(200).json({
      status: true,
      code: 200,
      message: "Blog deleted successfully",
      data: null
    });
  } catch (error) {
    console.error("Error deleting blog:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to delete blog",
      data: null
    });
  }
};

// Get blogs by category
const getBlogsByCategory = async (req, res) => {
  try {
    const { category } = req.params;
    const result = await blogservice.getBlogsByCategory(category, req.query);

    res.status(200).json({
      status: true,
      code: 200,
      message: `Blogs in category '${category}' retrieved successfully`,
      data: result
    });
  } catch (error) {
    console.error("Error fetching blogs by category:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch blogs by category",
      data: null
    });
  }
};

module.exports = {
  createBlogPost,
  getAllBlogs,
  getBlogById,
  updateBlog,
  deleteBlogById,
  getBlogsByCategory
};
