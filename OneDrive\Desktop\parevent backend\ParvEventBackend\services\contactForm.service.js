const Form = require("../models/contactForm.model");
const validator = require("validator");
const { sendContactEmail } = require("./email.service");
require("dotenv").config();

const createFormEntry = async (formData) => {
  try {
    const { first_name, last_name, email, phone, country, city, message, services } = formData;

    // Check for required fields
    if (!first_name) throw new Error("First name is required");
    if (!last_name) throw new Error("Last name is required");
    if (!email) throw new Error("Email is required");
    if (!phone) throw new Error("Phone number is required");
    if (!country) throw new Error("Country is required");
    if (!city) throw new Error("City is required");
    if (!services) throw new Error("Service selection is required");

    // Email validation
    if (!validator.isEmail(email)) {
      throw new Error("Invalid email");
    }

    // Phone validation (for India)
    if (!validator.isMobilePhone(phone, "en-IN")) {
      throw new Error("Invalid phone number");
    }

    // Services validation removed - will be handled by UI dropdown

    // Save form data
    const form = new Form(formData);
    const savedForm = await form.save();
    console.log(savedForm);

    // Send an email
    await sendContactEmail(formData);
    // await sendWhatsApp(formData);
    return savedForm;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createFormEntry,
};
