# 🎯 Hero Section API Documentation

## Overview
Complete Hero Section Management API for website homepage banners and hero content. Supports multiple hero sections with primary designation and carousel functionality.

## 🏗️ System Architecture

### Core Components
- **Model**: `models/heroSection.model.js` - Hero section data structure
- **Controller**: `controllers/heroSection.controller.js` - Request handling
- **Routes**: `routes/heroSection.route.js` - API endpoints
- **Base URL**: `/hero-section`

## 📊 Database Schema

### Hero Section Model
```javascript
{
  _id: ObjectId,
  title: String (required, max 200 chars),
  subtitle: String (optional, max 300 chars),
  description: String (optional, max 500 chars),
  image: String (required, hero image S3 URL),
  buttonText: String (max 50 chars, default: "Learn More"),
  buttonLink: String (default: "#"),
  isActive: Boolean (default: true),
  isPrimary: Boolean (default: false), // Only one can be primary
  sortOrder: Number (default: 0),
  displayDuration: Number (1000-30000ms, default: 5000),
  createdAt: Date,
  updatedAt: Date
}
```

### Key Features:
- **Primary Management**: Only one hero section can be primary at a time
- **Image Upload**: S3 `hero-section/` folder storage
- **Display Duration**: Configurable timing for carousel (1-30 seconds)
- **Button Customization**: Custom text and link
- **Sort Order**: Control display sequence
- **Active Status**: Enable/disable without deletion

## 🔗 API Endpoints

### 1. Create Hero Section (Admin)
```http
POST /hero-section
Content-Type: multipart/form-data
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:**
```javascript
{
  "title": "String (required, max 200 chars)",
  "subtitle": "String (optional, max 300 chars)",
  "description": "String (optional, max 500 chars)",
  "buttonText": "String (optional, max 50 chars, default: 'Learn More')",
  "buttonLink": "String (optional, default: '#')",
  "isPrimary": "Boolean (optional, default: false)",
  "sortOrder": "Number (optional, default: 0)",
  "displayDuration": "Number (optional, 1000-30000ms, default: 5000)",
  "isActive": "Boolean (optional, default: true)"
}
```

**Files:**
- `image` (required): Hero section background image (max 15MB)

**Response:**
```javascript
{
  "success": true,
  "message": "Hero section created successfully",
  "data": {
    "_id": "ObjectId",
    "title": "String",
    "subtitle": "String",
    "description": "String",
    "image": "String (S3 URL)",
    "buttonText": "String",
    "buttonLink": "String",
    "isPrimary": "Boolean",
    "sortOrder": "Number",
    "displayDuration": "Number",
    "createdAt": "Date",
    "updatedAt": "Date"
  }
}
```

### 2. Get All Hero Sections (Public)
```http
GET /hero-section?page=1&limit=10&sortBy=sortOrder&sortOrder=asc&activeOnly=true
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Sort field (default: sortOrder)
- `sortOrder`: asc/desc (default: asc)
- `activeOnly`: true/false (default: true)

**Response:**
```javascript
{
  "success": true,
  "data": {
    "heroSections": [
      {
        "_id": "ObjectId",
        "title": "String",
        "subtitle": "String",
        "description": "String",
        "image": "String (S3 URL)",
        "buttonText": "String",
        "buttonLink": "String",
        "isPrimary": "Boolean",
        "sortOrder": "Number",
        "displayDuration": "Number",
        "createdAt": "Date",
        "updatedAt": "Date"
      }
    ],
    "pagination": {
      "currentPage": "Number",
      "totalPages": "Number",
      "totalItems": "Number",
      "itemsPerPage": "Number",
      "hasNextPage": "Boolean",
      "hasPrevPage": "Boolean"
    }
  }
}
```

### 3. Get Primary Hero Section (Public)
```http
GET /hero-section/primary
```

**Response:** Single hero section object (the primary one)

### 4. Get Hero Section by ID (Public)
```http
GET /hero-section/:id
```

**Response:** Same as individual hero section object above

### 5. Update Hero Section (Admin)
```http
PUT /hero-section/:id
Content-Type: multipart/form-data
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:** Same as create, all fields optional
**Files:** `image` (optional): New hero section image

### 6. Delete Hero Section (Admin)
```http
DELETE /hero-section/:id
Authorization: Bearer <JWT_TOKEN>
```

**Response:**
```javascript
{
  "success": true,
  "message": "Hero section deleted successfully"
}
```

## 💡 Frontend Integration

### Basic Hero Section Display
```html
<!-- Hero Section Container -->
<div id="hero-container" class="hero-section">
  <!-- Hero content will be loaded here -->
</div>

<!-- CSS for Hero Section -->
<style>
.hero-section {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.hero-slide.active {
  opacity: 1;
}

.hero-content {
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.hero-description {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.hero-button {
  background: #007bff;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 5px;
  font-size: 1.1rem;
  text-decoration: none;
  display: inline-block;
  transition: background 0.3s ease;
}

.hero-button:hover {
  background: #0056b3;
  color: white;
  text-decoration: none;
}

.hero-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.hero-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s ease;
}

.hero-indicator.active {
  background: white;
}
</style>

<script>
let currentSlide = 0;
let heroSections = [];
let slideInterval;

// Fetch and display hero sections
async function loadHeroSections() {
  try {
    const response = await fetch('/hero-section?activeOnly=true&sortBy=sortOrder&sortOrder=asc');
    const result = await response.json();
    
    if (result.success && result.data.heroSections.length > 0) {
      heroSections = result.data.heroSections;
      displayHeroSections();
      startSlideshow();
    } else {
      // Load primary hero section as fallback
      loadPrimaryHero();
    }
  } catch (error) {
    console.error('Error loading hero sections:', error);
    loadPrimaryHero();
  }
}

// Load primary hero section
async function loadPrimaryHero() {
  try {
    const response = await fetch('/hero-section/primary');
    const result = await response.json();
    
    if (result.success) {
      heroSections = [result.data];
      displayHeroSections();
    }
  } catch (error) {
    console.error('Error loading primary hero:', error);
  }
}

function displayHeroSections() {
  const container = document.getElementById('hero-container');
  
  // Create slides
  const slidesHTML = heroSections.map((hero, index) => `
    <div class="hero-slide ${index === 0 ? 'active' : ''}" 
         style="background-image: url('${hero.image}')">
      <div class="hero-content">
        <h1 class="hero-title">${hero.title}</h1>
        ${hero.subtitle ? `<h2 class="hero-subtitle">${hero.subtitle}</h2>` : ''}
        ${hero.description ? `<p class="hero-description">${hero.description}</p>` : ''}
        ${hero.buttonText && hero.buttonLink ? `
          <a href="${hero.buttonLink}" class="hero-button">${hero.buttonText}</a>
        ` : ''}
      </div>
    </div>
  `).join('');
  
  // Create indicators
  const indicatorsHTML = heroSections.length > 1 ? `
    <div class="hero-indicators">
      ${heroSections.map((_, index) => `
        <div class="hero-indicator ${index === 0 ? 'active' : ''}" 
             onclick="goToSlide(${index})"></div>
      `).join('')}
    </div>
  ` : '';
  
  container.innerHTML = slidesHTML + indicatorsHTML;
}

function startSlideshow() {
  if (heroSections.length <= 1) return;
  
  slideInterval = setInterval(() => {
    nextSlide();
  }, heroSections[currentSlide]?.displayDuration || 5000);
}

function nextSlide() {
  if (heroSections.length <= 1) return;
  
  const slides = document.querySelectorAll('.hero-slide');
  const indicators = document.querySelectorAll('.hero-indicator');
  
  // Remove active class from current slide
  slides[currentSlide].classList.remove('active');
  indicators[currentSlide]?.classList.remove('active');
  
  // Move to next slide
  currentSlide = (currentSlide + 1) % heroSections.length;
  
  // Add active class to new slide
  slides[currentSlide].classList.add('active');
  indicators[currentSlide]?.classList.add('active');
  
  // Restart interval with new slide's duration
  clearInterval(slideInterval);
  startSlideshow();
}

function goToSlide(index) {
  if (index === currentSlide) return;
  
  const slides = document.querySelectorAll('.hero-slide');
  const indicators = document.querySelectorAll('.hero-indicator');
  
  // Remove active class from current slide
  slides[currentSlide].classList.remove('active');
  indicators[currentSlide]?.classList.remove('active');
  
  // Set new slide
  currentSlide = index;
  
  // Add active class to new slide
  slides[currentSlide].classList.add('active');
  indicators[currentSlide]?.classList.add('active');
  
  // Restart slideshow
  clearInterval(slideInterval);
  startSlideshow();
}

// Load hero sections when page loads
document.addEventListener('DOMContentLoaded', loadHeroSections);

// Pause slideshow on hover
document.addEventListener('DOMContentLoaded', () => {
  const heroContainer = document.getElementById('hero-container');
  heroContainer.addEventListener('mouseenter', () => clearInterval(slideInterval));
  heroContainer.addEventListener('mouseleave', startSlideshow);
});
</script>
```

## 🚀 Admin Panel Usage Guide

### Creating Hero Sections:

#### Single Hero Section:
1. Upload hero image (max 15MB)
2. Fill title (required)
3. Add subtitle and description (optional)
4. Set button text and link (optional)
5. Set as primary if this should be the main hero
6. Configure display duration (1-30 seconds)
7. Set sort order for multiple heroes
8. Submit

#### Multiple Hero Sections (Carousel):
1. Create first hero section with `isPrimary: true`
2. Create additional hero sections with `isPrimary: false`
3. Set different `sortOrder` values (0, 1, 2, etc.)
4. Configure individual `displayDuration` for each slide
5. All will automatically form a carousel

### Primary Hero Management:
- Only **one hero section can be primary** at a time
- Setting a new hero as primary automatically removes primary status from others
- Primary hero is used as fallback when carousel fails to load

## 🔍 Testing & Debugging

### Test Hero Section Display:
```javascript
// Check hero sections availability
async function testHeroSections() {
  try {
    // Test all hero sections
    const allResponse = await fetch('/hero-section');
    const allResult = await allResponse.json();
    console.log('All Hero Sections:', allResult);

    // Test primary hero section
    const primaryResponse = await fetch('/hero-section/primary');
    const primaryResult = await primaryResponse.json();
    console.log('Primary Hero Section:', primaryResult);

    // Test specific hero section
    if (allResult.data.heroSections.length > 0) {
      const id = allResult.data.heroSections[0]._id;
      const singleResponse = await fetch(`/hero-section/${id}`);
      const singleResult = await singleResponse.json();
      console.log('Single Hero Section:', singleResult);
    }
  } catch (error) {
    console.error('Hero section test failed:', error);
  }
}

// Run test
testHeroSections();
```

## 🎯 Best Practices

### For Developers:
1. **Always implement fallback** to primary hero section
2. **Handle empty states** gracefully
3. **Optimize images** before display
4. **Implement responsive design** for different screen sizes
5. **Add loading states** while fetching data

### For Admins:
1. **Use high-quality images** (1920x1080 or higher)
2. **Keep titles concise** and impactful
3. **Test display duration** for readability
4. **Maintain consistent branding** across multiple heroes
5. **Set logical sort order** for carousel flow

### For Performance:
1. **Optimize hero images** before upload (compress, resize)
2. **Use appropriate image formats** (JPEG for photos)
3. **Implement lazy loading** for non-primary heroes
4. **Cache hero data** when possible
5. **Preload next slide** in carousel

## 🔧 Troubleshooting

### Hero Section Not Displaying:
1. **Check API response**: Verify hero section data is returned
2. **Check image URLs**: Ensure S3 URLs are accessible
3. **Test primary endpoint**: Use `/hero-section/primary` as fallback
4. **Check browser console**: Look for JavaScript errors
5. **Verify CSS**: Ensure hero styles are loaded

### Carousel Not Working:
1. **Check multiple heroes**: Ensure more than one active hero exists
2. **Verify display duration**: Check if values are valid (1000-30000ms)
3. **Check JavaScript**: Ensure slideshow functions are working
4. **Test manually**: Use `goToSlide()` function to test navigation

### Primary Hero Issues:
1. **Check database**: Ensure one hero has `isPrimary: true`
2. **Test endpoint**: Call `/hero-section/primary` directly
3. **Check active status**: Ensure primary hero has `isActive: true`
4. **Verify creation**: Create new primary hero if none exists

## 📋 API Response Examples

### Successful Creation:
```json
{
  "success": true,
  "message": "Hero section created successfully",
  "data": {
    "_id": "64f1234567890abcdef12345",
    "title": "Welcome to ParvEvent",
    "subtitle": "Creating Memorable Moments",
    "description": "We specialize in making your special day unforgettable",
    "image": "https://s3.amazonaws.com/bucket/hero-section/1703123456789-abc123.jpg",
    "buttonText": "Get Started",
    "buttonLink": "/contact",
    "isPrimary": true,
    "sortOrder": 0,
    "displayDuration": 5000,
    "createdAt": "2023-12-21T10:30:00.000Z",
    "updatedAt": "2023-12-21T10:30:00.000Z"
  }
}
```

### Validation Error:
```json
{
  "success": false,
  "message": "Validation error",
  "errors": [
    "Title is required",
    "Display duration must be at least 1 second"
  ]
}
```

### Not Found Error:
```json
{
  "success": false,
  "message": "Hero section not found"
}
```

This documentation provides complete coverage of the Hero Section API with multiple creation support and carousel functionality.
