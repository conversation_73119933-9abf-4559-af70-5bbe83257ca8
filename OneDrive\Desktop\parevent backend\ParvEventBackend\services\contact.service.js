const Contact = require("../models/contact.model");
const validator = require("validator");
const { sendContactFormEmail } = require("./email.service");

// Create new contact
const createContact = async (contactData) => {
  try {
    const { name, email, countryCode, phoneNumber, service, message, status, priority } = contactData;

    // Validate required fields
    if (!name) throw new Error("Name is required");
    if (!email) throw new Error("Email is required");
    if (!countryCode) throw new Error("Country code is required");
    if (!phoneNumber) throw new Error("Phone number is required");
    if (!service) throw new Error("Service is required");
    if (!message) throw new Error("Message is required");

    // Email validation
    if (!validator.isEmail(email)) {
      throw new Error("Invalid email format");
    }

    // Phone validation
    const phoneRegex = /^[\d\s\-]{8,15}$/;
    if (!phoneRegex.test(phoneNumber.replace(/\s|-/g, ''))) {
      throw new Error("Invalid phone number format");
    }

    // Service validation removed - will be handled by UI dropdown

    // Create new contact
    const contact = new Contact({
      name: name.trim(),
      email: email.toLowerCase().trim(),
      countryCode: countryCode.trim(),
      phoneNumber: phoneNumber.trim(),
      service,
      message: message.trim(),
      status: status || "new",
      priority: priority || "medium"
    });

    const savedContact = await contact.save();

    // Send emails after successful contact creation
    try {
      await sendContactFormEmail(contactData);
      console.log("📧 Emails sent successfully!");
    } catch (emailError) {
      console.error("📧 Email sending failed:", emailError.message);
      // Don't throw error here - contact is already saved
      // Just log the email error
    }

    return savedContact;
  } catch (error) {
    throw error;
  }
};

// Get all contacts with filtering and pagination
const getAllContacts = async (filters = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      service,
      priority,
      search,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = filters;

    // Build query object
    const query = {};

    if (status) query.status = status;
    if (service) query.service = service;
    if (priority) query.priority = priority;

    // Search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
        { phoneNumber: { $regex: search, $options: "i" } },
        { message: { $regex: search, $options: "i" } }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === "desc" ? -1 : 1;

    // Execute query with pagination
    const contacts = await Contact.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalContacts = await Contact.countDocuments(query);
    const totalPages = Math.ceil(totalContacts / limit);

    return {
      contacts,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalContacts,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

// Get contact by ID
const getContactById = async (contactId) => {
  try {
    if (!contactId) {
      throw new Error("Contact ID is required");
    }

    const contact = await Contact.findById(contactId);
    if (!contact) {
      throw new Error("Contact not found");
    }

    return contact;
  } catch (error) {
    throw error;
  }
};

// Update contact
const updateContact = async (contactId, updateData) => {
  try {
    if (!contactId) {
      throw new Error("Contact ID is required");
    }

    // Validate email if provided
    if (updateData.email && !validator.isEmail(updateData.email)) {
      throw new Error("Invalid email format");
    }

    // Validate phone if provided
    if (updateData.phoneNumber) {
      const phoneRegex = /^[\d\s\-]{8,15}$/;
      if (!phoneRegex.test(updateData.phoneNumber.replace(/\s|-/g, ''))) {
        throw new Error("Invalid phone number format");
      }
    }

    // Service validation removed - will be handled by UI dropdown

    // Clean up data
    if (updateData.name) updateData.name = updateData.name.trim();
    if (updateData.email) updateData.email = updateData.email.toLowerCase().trim();
    if (updateData.countryCode) updateData.countryCode = updateData.countryCode.trim();
    if (updateData.phoneNumber) updateData.phoneNumber = updateData.phoneNumber.trim();
    if (updateData.message) updateData.message = updateData.message.trim();

    const updatedContact = await Contact.findByIdAndUpdate(
      contactId,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedContact) {
      throw new Error("Contact not found");
    }

    return updatedContact;
  } catch (error) {
    throw error;
  }
};

// Delete contact
const deleteContact = async (contactId) => {
  try {
    if (!contactId) {
      throw new Error("Contact ID is required");
    }

    const deletedContact = await Contact.findByIdAndDelete(contactId);
    if (!deletedContact) {
      throw new Error("Contact not found");
    }

    return deletedContact;
  } catch (error) {
    throw error;
  }
};

// Get contact statistics
const getContactStatistics = async () => {
  try {
    const totalContacts = await Contact.countDocuments();
    
    const statusStats = await Contact.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } }
    ]);

    const serviceStats = await Contact.aggregate([
      { $group: { _id: "$service", count: { $sum: 1 } } }
    ]);

    const priorityStats = await Contact.aggregate([
      { $group: { _id: "$priority", count: { $sum: 1 } } }
    ]);

    return {
      totalContacts,
      statusStats,
      serviceStats,
      priorityStats
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createContact,
  getAllContacts,
  getContactById,
  updateContact,
  deleteContact,
  getContactStatistics
};
