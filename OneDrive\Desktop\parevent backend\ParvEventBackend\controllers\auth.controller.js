const authService = require('../services/auth.service');
const { StatusCodes } = require('http-status-codes');
const Joi = require('joi');

// Validation schemas
const signupSchema = Joi.object({
    firstname: Joi.string()
        .min(1)
        .max(50)
        .required()
        .messages({
            'string.min': 'First name must be at least 1 character',
            'string.max': 'First name cannot exceed 50 characters',
            'any.required': 'First name is required'
        }),
    lastname: Joi.string()
        .min(1)
        .max(50)
        .required()
        .messages({
            'string.min': 'Last name must be at least 1 character',
            'string.max': 'Last name cannot exceed 50 characters',
            'any.required': 'Last name is required'
        }),
    email: Joi.string()
        .required()
        .messages({
            'any.required': 'Email is required'
        }),
    password: Joi.string()
        .min(4)
        .required()
        .messages({
            'string.min': 'Password must be at least 4 characters',
            'any.required': 'Password is required'
        }),
    confirmpassword: Joi.string()
        .valid(Joi.ref('password'))
        .messages({
            'any.only': 'Passwords do not match'
        })
});

const loginSchema = Joi.object({
    email: Joi.string()
        .required()
        .messages({
            'any.required': 'Email is required'
        }),
    password: Joi.string()
        .required()
        .messages({
            'any.required': 'Password is required'
        }),
    otpToken: Joi.string()
        .length(6)
        .pattern(/^\d+$/)
        .optional()
        .messages({
            'string.length': '2FA token must be 6 digits',
            'string.pattern.base': '2FA token must contain only numbers'
        })
});

const verifyEmailSchema = Joi.object({
    email: Joi.string()
        .required()
        .messages({
            'any.required': 'Email is required'
        }),
    otp: Joi.string()
        .length(6)
        .pattern(/^\d+$/)
        .required()
        .messages({
            'string.length': 'OTP must be 6 digits',
            'string.pattern.base': 'OTP must contain only numbers',
            'any.required': 'OTP is required'
        })
});

const verify2FASchema = Joi.object({
    adminId: Joi.string()
        .required()
        .messages({
            'any.required': 'Admin ID is required'
        }),
    token: Joi.string()
        .length(6)
        .pattern(/^\d+$/)
        .required()
        .messages({
            'string.length': '2FA token must be 6 digits',
            'string.pattern.base': '2FA token must contain only numbers',
            'any.required': '2FA token is required'
        })
});

// Admin signup
const signup = async (req, res) => {
    try {
        console.log('📝 Signup request received:', req.body);

        // Validate request body
        const { error, value } = signupSchema.validate(req.body);
        if (error) {
            console.log('❌ Validation error:', error.details);
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        console.log('✅ Validation passed, calling auth service...');

        // Call auth service
        const result = await authService.signup(value);

        console.log('✅ Signup successful:', result.adminId);

        res.status(StatusCodes.CREATED).json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('❌ Signup error:', error);

        // Handle specific error types
        let statusCode = StatusCodes.BAD_REQUEST;
        let message = error.message || 'Signup failed';

        if (error.message && error.message.includes('already exists')) {
            statusCode = StatusCodes.CONFLICT;
        } else if (error.name === 'ValidationError') {
            statusCode = StatusCodes.BAD_REQUEST;
            message = 'Invalid data provided';
        } else if (error.name === 'MongoError' || error.name === 'MongooseError') {
            statusCode = StatusCodes.INTERNAL_SERVER_ERROR;
            message = 'Database error occurred';
        }

        res.status(statusCode).json({
            success: false,
            message: message,
            ...(process.env.NODE_ENV === 'development' && { debug: error.stack })
        });
    }
};

// Verify email OTP
const verifyEmail = async (req, res) => {
    try {
        // Validate request body
        const { error, value } = verifyEmailSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Call auth service
        const result = await authService.verifyEmailOTP(value.email, value.otp);

        res.status(StatusCodes.OK).json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('Email verification error:', error);
        res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: error.message || 'Email verification failed'
        });
    }
};

// Verify 2FA setup
const verify2FA = async (req, res) => {
    try {
        // Validate request body
        const { error, value } = verify2FASchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Call auth service
        const result = await authService.verify2FA(value.adminId, value.token);

        res.status(StatusCodes.OK).json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('2FA verification error:', error);
        res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: error.message || '2FA verification failed'
        });
    }
};

// Admin login
const login = async (req, res) => {
    try {
        // Validate request body
        const { error, value } = loginSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Call auth service
        const result = await authService.login(value.email, value.password, value.otpToken);

        // Set JWT token in HTTP-only cookie
        res.cookie('adminToken', result.token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 60 * 60 * 1000 // 1 hour
        });

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                message: result.message,
                admin: result.admin,
                token: result.token
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(StatusCodes.UNAUTHORIZED).json({
            success: false,
            message: error.message || 'Login failed'
        });
    }
};

// Resend email OTP
const resendEmailOTP = async (req, res) => {
    try {
        const { email } = req.body;

        if (!email) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Email is required'
            });
        }

        // Call auth service
        const result = await authService.resendEmailOTP(email);

        res.status(StatusCodes.OK).json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('Resend OTP error:', error);
        res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: error.message || 'Failed to resend OTP'
        });
    }
};

// Admin logout
const logout = async (req, res) => {
    try {
        // Clear the JWT cookie
        res.clearCookie('adminToken');

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Logged out successfully'
        });

    } catch (error) {
        console.error('Logout error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Logout failed'
        });
    }
};

// Get current admin profile
const getProfile = async (req, res) => {
    try {
        const adminId = req.admin.adminId;
        const Admin = require('../models/admin.model');

        const admin = await Admin.findById(adminId);
        if (!admin) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Admin not found'
            });
        }

        const adminData = admin.getDecryptedData();

        res.status(StatusCodes.OK).json({
            success: true,
            data: adminData
        });

    } catch (error) {
        console.error('Get profile error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to get profile'
        });
    }
};

// Simple login for testing (no 2FA required)
const simpleLogin = async (req, res) => {
    try {
        const { email, password } = req.body;

        console.log(`🔍 Simple login attempt for: ${email}`);

        if (!email || !password) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Email and password are required'
            });
        }

        // Find admin by email
        const Admin = require('../models/admin.model');
        console.log(`🔍 Searching for admin with email: ${email}`);

        const admin = await Admin.findByEmail(email);
        console.log(`🔍 Admin found:`, admin ? 'Yes' : 'No');

        if (!admin) {
            // Try to find by any method for debugging
            const allAdmins = await Admin.find({});
            console.log(`📋 Total admins in database: ${allAdmins.length}`);
            allAdmins.forEach((a, index) => {
                try {
                    const decrypted = a.getDecryptedData();
                    console.log(`Admin ${index + 1}: ${decrypted.email}`);
                } catch (err) {
                    console.log(`Admin ${index + 1}: [decryption failed]`);
                }
            });

            return res.status(StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Invalid credentials - Admin not found'
            });
        }

        console.log(`🔍 Checking password for admin: ${admin._id}`);

        // Check password
        const isPasswordValid = await admin.comparePassword(password);
        console.log(`🔍 Password valid:`, isPasswordValid);

        if (!isPasswordValid) {
            return res.status(StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Invalid credentials - Wrong password'
            });
        }

        // Generate token
        const authService = require('../services/auth.service');
        const token = authService.generateToken(admin._id);

        // Get decrypted data
        const adminData = admin.getDecryptedData();

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                message: 'Simple login successful',
                admin: adminData,
                token: token,
                note: 'This is a test login without 2FA verification'
            }
        });

    } catch (error) {
        console.error('Simple login error:', error);
        res.status(StatusCodes.UNAUTHORIZED).json({
            success: false,
            message: error.message || 'Login failed'
        });
    }
};

// Debug endpoint to list all admins
const debugAdmins = async (req, res) => {
    try {
        const Admin = require('../models/admin.model');
        const allAdmins = await Admin.find({});

        const adminList = allAdmins.map((admin, index) => {
            try {
                const decrypted = admin.getDecryptedData();
                return {
                    index: index + 1,
                    id: admin._id,
                    email: decrypted.email,
                    firstname: decrypted.firstname,
                    lastname: decrypted.lastname,
                    isVerified: admin.isVerified,
                    otpVerified: admin.otpVerified,
                    isActive: admin.isActive
                };
            } catch (err) {
                return {
                    index: index + 1,
                    id: admin._id,
                    error: 'Decryption failed',
                    rawEmail: admin.email
                };
            }
        });

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                totalAdmins: allAdmins.length,
                admins: adminList
            }
        });

    } catch (error) {
        console.error('Debug admins error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch admins'
        });
    }
};

// Direct login without encryption (for debugging)
const directLogin = async (req, res) => {
    try {
        const { email, password, adminId } = req.body;

        console.log(`🔍 Direct login attempt for: ${email || adminId}`);

        const Admin = require('../models/admin.model');
        let admin;

        if (adminId) {
            // Login by admin ID
            admin = await Admin.findById(adminId);
            console.log(`🔍 Admin found by ID:`, admin ? 'Yes' : 'No');
        } else if (email) {
            // Try multiple methods to find admin by email
            const allAdmins = await Admin.find({});
            console.log(`📋 Searching through ${allAdmins.length} admins for email: ${email}`);

            // Method 1: Try findByEmail function
            try {
                admin = await Admin.findByEmail(email);
                if (admin) {
                    console.log(`✅ Found admin using findByEmail method`);
                }
            } catch (err) {
                console.log(`❌ findByEmail failed: ${err.message}`);
            }

            // Method 2: Search through all admins if Method 1 failed
            if (!admin) {
                for (let i = 0; i < allAdmins.length; i++) {
                    try {
                        const decrypted = allAdmins[i].getDecryptedData();
                        console.log(`🔍 Checking admin ${i}: ${decrypted.email}`);
                        if (decrypted.email === email) {
                            admin = allAdmins[i];
                            console.log(`✅ Found admin by decrypted email at index ${i}`);
                            break;
                        }
                    } catch (err) {
                        console.log(`❌ Decryption failed for admin ${i}: ${err.message}`);
                    }
                }
            }

            // Method 3: Try to find by firstname/lastname if email fails
            if (!admin) {
                console.log(`🔍 Trying to find by name pattern...`);
                for (let i = 0; i < allAdmins.length; i++) {
                    try {
                        const decrypted = allAdmins[i].getDecryptedData();
                        // Check if this looks like gulshan kumar
                        if (decrypted.firstname && decrypted.firstname.toLowerCase().includes('gulshan')) {
                            admin = allAdmins[i];
                            console.log(`✅ Found admin by firstname match at index ${i}`);
                            break;
                        }
                    } catch (err) {
                        // Skip this admin
                    }
                }
            }
        }

        if (!admin) {
            return res.status(StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Admin not found. Try using adminId instead of email.'
            });
        }

        console.log(`🔍 Checking password for admin: ${admin._id}`);

        // Check password
        const isPasswordValid = await admin.comparePassword(password);
        console.log(`🔍 Password valid:`, isPasswordValid);

        if (!isPasswordValid) {
            return res.status(StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Invalid password'
            });
        }

        // Generate token
        const authService = require('../services/auth.service');
        const token = authService.generateToken(admin._id);

        // Get admin data (try decryption, fallback to basic info)
        let adminData;
        try {
            adminData = admin.getDecryptedData();
        } catch (err) {
            adminData = {
                _id: admin._id,
                firstname: 'Encrypted',
                lastname: 'Data',
                email: 'Decryption Failed',
                role: admin.role || 'admin',
                isVerified: admin.isVerified,
                otpVerified: admin.otpVerified,
                isActive: admin.isActive
            };
        }

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                message: 'Direct login successful',
                admin: adminData,
                token: token,
                note: 'This is a direct login bypassing encryption issues'
            }
        });

    } catch (error) {
        console.error('Direct login error:', error);
        res.status(StatusCodes.UNAUTHORIZED).json({
            success: false,
            message: error.message || 'Login failed'
        });
    }
};

// Universal login (works with any admin by email and password)
const emailLogin = async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Email and password are required'
            });
        }

        console.log(`🔍 Universal login attempt for: ${email}`);

        const Admin = require('../models/admin.model');
        const allAdmins = await Admin.find({});
        let admin = null;

        console.log(`📋 Total admins in database: ${allAdmins.length}`);

        // Strategy 1: Try to find admin by encrypted email match
        for (let i = 0; i < allAdmins.length; i++) {
            try {
                const decrypted = allAdmins[i].getDecryptedData();
                if (decrypted.email && decrypted.email.toLowerCase() === email.toLowerCase()) {
                    admin = allAdmins[i];
                    console.log(`✅ Found admin by email match at index ${i}`);
                    break;
                }
            } catch (err) {
                console.log(`❌ Failed to decrypt admin ${i}: ${err.message}`);
            }
        }

        // Strategy 2: If not found by email, try password with all admins
        if (!admin) {
            console.log(`🔍 Email not found, trying password with all admins...`);

            for (let i = 0; i < allAdmins.length; i++) {
                try {
                    const isPasswordValid = await allAdmins[i].comparePassword(password);
                    if (isPasswordValid) {
                        admin = allAdmins[i];
                        console.log(`✅ Password matched for admin at index ${i}`);
                        break;
                    }
                } catch (err) {
                    console.log(`❌ Password check failed for admin ${i}: ${err.message}`);
                }
            }
        }

        if (!admin) {
            // Debug: Show all admin emails for troubleshooting
            console.log(`📋 Available admin emails:`);
            for (let i = 0; i < allAdmins.length; i++) {
                try {
                    const decrypted = allAdmins[i].getDecryptedData();
                    console.log(`Admin ${i}: ${decrypted.email} (ID: ${allAdmins[i]._id})`);
                } catch (err) {
                    console.log(`Admin ${i}: [decryption failed] (ID: ${allAdmins[i]._id})`);
                }
            }

            return res.status(StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Invalid email or password. No matching admin found.'
            });
        }

        console.log(`🔍 Verifying password for admin: ${admin._id}`);

        // Final password verification
        const isPasswordValid = await admin.comparePassword(password);
        if (!isPasswordValid) {
            return res.status(StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Invalid password'
            });
        }

        // Generate token
        const authService = require('../services/auth.service');
        const token = authService.generateToken(admin._id);

        // Get admin data
        let adminData;
        try {
            adminData = admin.getDecryptedData();
        } catch (err) {
            console.log(`❌ Failed to decrypt admin data: ${err.message}`);
            adminData = {
                _id: admin._id,
                firstname: 'Admin',
                lastname: 'User',
                email: email,
                role: admin.role || 'admin',
                isVerified: admin.isVerified,
                otpVerified: admin.otpVerified,
                isActive: admin.isActive
            };
        }

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                message: 'Login successful',
                admin: adminData,
                token: token
            }
        });

    } catch (error) {
        console.error('Universal login error:', error);
        res.status(StatusCodes.UNAUTHORIZED).json({
            success: false,
            message: error.message || 'Login failed'
        });
    }
};

// Quick login by admin ID (for easy testing)
const quickLogin = async (req, res) => {
    try {
        const { adminId, password } = req.body;

        if (!adminId) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Admin ID is required'
            });
        }

        console.log(`🔍 Quick login attempt for admin ID: ${adminId}`);

        const Admin = require('../models/admin.model');
        const admin = await Admin.findById(adminId);

        if (!admin) {
            return res.status(StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Admin not found with this ID'
            });
        }

        // If password is provided, verify it
        if (password) {
            const isPasswordValid = await admin.comparePassword(password);
            if (!isPasswordValid) {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Invalid password'
                });
            }
        }

        // Generate token
        const authService = require('../services/auth.service');
        const token = authService.generateToken(admin._id);

        // Get admin data
        let adminData;
        try {
            adminData = admin.getDecryptedData();
        } catch (err) {
            console.log(`❌ Failed to decrypt admin data: ${err.message}`);
            adminData = {
                _id: admin._id,
                firstname: 'Admin',
                lastname: 'User',
                email: '<EMAIL>',
                role: admin.role || 'admin',
                isVerified: admin.isVerified,
                otpVerified: admin.otpVerified,
                isActive: admin.isActive
            };
        }

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                message: 'Quick login successful',
                admin: adminData,
                token: token
            }
        });

    } catch (error) {
        console.error('Quick login error:', error);
        res.status(StatusCodes.UNAUTHORIZED).json({
            success: false,
            message: error.message || 'Login failed'
        });
    }
};

// Simple test signup without database (for testing)
const testSignup = async (req, res) => {
    try {
        console.log('🧪 Test signup request received:', req.body);

        // Validate request body
        const { error, value } = signupSchema.validate(req.body);
        if (error) {
            console.log('❌ Validation error:', error.details);
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        console.log('✅ Validation passed for test signup');

        // Generate mock response without database
        const mockAdminId = 'test_' + Date.now();
        const emailOTP = Math.floor(100000 + Math.random() * 900000).toString();

        // Store OTP in memory for testing
        global.emailOTPs = global.emailOTPs || {};
        global.emailOTPs[value.email] = {
            otp: emailOTP,
            expires: Date.now() + 10 * 60 * 1000, // 10 minutes
            adminId: mockAdminId
        };

        console.log(`🔐 Test OTP for ${value.email}: ${emailOTP}`);

        res.status(StatusCodes.CREATED).json({
            success: true,
            data: {
                message: 'Test admin registered successfully. Please verify your email.',
                adminId: mockAdminId,
                email: value.email,
                otp: emailOTP, // Include OTP in response for testing
                note: 'This is a test endpoint - no data is saved to database'
            }
        });

    } catch (error) {
        console.error('❌ Test signup error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Test signup failed',
            error: error.message
        });
    }
};

module.exports = {
    signup,
    verifyEmail,
    verify2FA,
    login,
    logout,
    resendEmailOTP,
    getProfile,
    simpleLogin,
    debugAdmins,
    directLogin,
    emailLogin,
    quickLogin,
    testSignup
};
