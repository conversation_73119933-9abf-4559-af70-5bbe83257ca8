{"info": {"name": "ParvEvent Backend APIs", "description": "Complete API collection for ParvEvent backend with all CRUD operations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8005/api", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>up", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstname\": \"Admin\",\n  \"lastname\": \"User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\",\n  \"confirmpassword\": \"admin123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/signup", "host": ["{{baseUrl}}"], "path": ["auth", "signup"]}}}, {"name": "Test Signup (No Database)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstname\": \"Test\",\n  \"lastname\": \"User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"test123\",\n  \"confirmpassword\": \"test123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/test-signup", "host": ["{{baseUrl}}"], "path": ["auth", "test-signup"]}}}, {"name": "Email Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["auth", "verify-email"]}}}, {"name": "2FA Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"token\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/verify-2fa", "host": ["{{baseUrl}}"], "path": ["auth", "verify-2fa"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\",\n  \"token\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}]}, {"name": "Gallery", "item": [{"name": "Create Gallery Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Wedding Gallery", "type": "text"}, {"key": "description", "value": "Beautiful wedding moments", "type": "text"}, {"key": "keywords", "value": "[\"wedding\", \"photography\", \"celebration\"]", "type": "text"}, {"key": "category", "value": "wedding", "type": "text"}, {"key": "sortOrder", "value": "1", "type": "text"}, {"key": "image", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/gallery", "host": ["{{baseUrl}}"], "path": ["gallery"]}}}, {"name": "Get All Gallery Items", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/gallery?page=1&limit=10&category=wedding", "host": ["{{baseUrl}}"], "path": ["gallery"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "category", "value": "wedding"}]}}}, {"name": "Get Gallery by ID", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/gallery/:id", "host": ["{{baseUrl}}"], "path": ["gallery", ":id"], "variable": [{"key": "id", "value": "gallery_1703123456789_abc123def"}]}}}, {"name": "Update Gallery Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Updated Wedding Gallery", "type": "text"}, {"key": "description", "value": "Updated description", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/gallery/:id", "host": ["{{baseUrl}}"], "path": ["gallery", ":id"], "variable": [{"key": "id", "value": "gallery_1703123456789_abc123def"}]}}}, {"name": "Delete Gallery Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/gallery/:id", "host": ["{{baseUrl}}"], "path": ["gallery", ":id"], "variable": [{"key": "id", "value": "gallery_1703123456789_abc123def"}]}}}]}, {"name": "Reviews", "item": [{"name": "Create Review", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "<PERSON>", "type": "text"}, {"key": "relationship", "value": "groom", "type": "text"}, {"key": "review", "value": "Amazing service! Highly recommended.", "type": "text"}, {"key": "star", "value": "5", "type": "text"}, {"key": "eventType", "value": "wedding", "type": "text"}, {"key": "image", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/reviews", "host": ["{{baseUrl}}"], "path": ["reviews"]}}}, {"name": "Get All Reviews", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/reviews?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["reviews"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Review Statistics", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/reviews/stats", "host": ["{{baseUrl}}"], "path": ["reviews", "stats"]}}}]}]}