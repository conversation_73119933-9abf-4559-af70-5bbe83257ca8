const formService = require('../services/contactForm.service');

const submitForm = async (req, res) => {
  try {
    const formData = req.body;
    const savedForm = await formService.createFormEntry(formData);
    res.status(201).json({ success: true, message: "Form submitted successfully", data: savedForm });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

module.exports = {
  submitForm,
};
