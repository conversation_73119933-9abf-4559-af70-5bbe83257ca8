# 🛠️ Services API Documentation (Simplified)

## Overview
Simplified API documentation for Services management in ParvEvent Backend. Simple service system with only essential fields.

## Base URL
```
http://localhost:8005/api/services
```

## Authentication
All endpoints require admin authentication:
```
Authorization: Bearer <JWT_TOKEN>
```

---

## 📋 **API Endpoints**

### 1. Create Service
**POST** `/api/services`

Creates a new service with SVG icon and image uploads.

**Content-Type:** `multipart/form-data`

**Form Fields:**
```javascript
{
  "title": "Web Development",                    // Required, max 200 chars
  "description": "Primary description",         // Required, max 1000 chars
  "description2": "Secondary description",      // Optional, max 1000 chars
  "howWeDoIt": [                               // Optional array for accordion
    {
      "title": "Step 1: Planning",             // Optional, max 200 chars
      "description": "We start with planning"  // Optional, max 2000 chars
    },
    {
      "title": "Step 2: Development",          // Optional, max 200 chars
      "description": "We develop the solution" // Optional, max 2000 chars
    }
  ],
  "sortOrder": 1                                // Optional, default 0
}

// File uploads (both required):
icons: File                    // SVG icon file (required)
image: File                    // Service image (required)
```

**Response:**
```javascript
{
  "success": true,
  "message": "Service created successfully",
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "title": "Web Development",
    "description": "Primary description",
    "description2": "Secondary description",
    "icons": "https://s3.amazonaws.com/bucket/icons/web-dev.svg",
    "image": "https://s3.amazonaws.com/bucket/images/web-dev.jpg",
    "howWeDoIt": {
      "title": "How We Do It",
      "description": "Our process explanation"
    },
    "sortOrder": 1,
    "isActive": true,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

### 2. Get All Services
**GET** `/api/services`

Retrieves all active services with pagination and filtering.

**Query Parameters:**
```
?page=1                    // Page number (default: 1)
&limit=10                  // Items per page (default: 10, max: 100)
&sortBy=createdAt          // Sort field (default: createdAt)
&sortOrder=desc            // Sort order: asc/desc (default: desc)
&search=web                // Search in title and descriptions
```

**Response:**
```javascript
{
  "success": true,
  "data": {
    "services": [
      {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
        "title": "Web Development",
        "description": "Primary description",
        "description2": "Secondary description",
        "icons": "https://s3.amazonaws.com/bucket/icons/web-dev.svg",
        "image": "https://s3.amazonaws.com/bucket/images/web-dev.jpg",
        "howWeDoIt": {
          "title": "How We Do It",
          "description": "Our process explanation"
        },
        "sortOrder": 1,
        "isActive": true,
        "createdAt": "2023-12-01T10:00:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 45,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

### 3. Get Service by ID
**GET** `/api/services/:id`

Retrieves a specific service by MongoDB ObjectId and increments view count.

**Parameters:**
- `id` - MongoDB ObjectId

**Response:**
```javascript
{
  "success": true,
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "title": "Web Development",
    "description": "Primary description",
    "description2": "Secondary description",
    "icons": "https://s3.amazonaws.com/bucket/icons/web-dev.svg",
    "image": "https://s3.amazonaws.com/bucket/images/web-dev.jpg",
    "howWeDoIt": {
      "title": "How We Do It",
      "description": "Our detailed process explanation"
    },
    "sortOrder": 1,
    "isActive": true,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

### 4. Update Service
**PUT** `/api/services/:id`

Updates an existing service. Supports both JSON and multipart/form-data.

**Content-Type:** `multipart/form-data` or `application/json`

**Parameters:**
- `id` - MongoDB ObjectId

**Form Fields:** (All optional)
```javascript
{
  "title": "Updated Web Development",
  "description": "Updated primary description",
  "description2": "Updated secondary description",
  "howWeDoIt": {
    "title": "Updated How We Do It",
    "description": "Updated process explanation"
  },
  "sortOrder": 2
}

// File uploads (optional):
icons: File                    // New SVG icon
image: File                    // New service image
```

**Response:**
```javascript
{
  "success": true,
  "message": "Service updated successfully",
  "data": {
    // Updated service object
  }
}
```

### 5. Delete Service
**DELETE** `/api/services/:id`

Soft deletes a service (sets isActive: false).

**Parameters:**
- `id` - MongoDB ObjectId

**Response:**
```javascript
{
  "success": true,
  "message": "Service deleted successfully",
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "title": "Web Development",
    "isActive": false
  }
}
```

### 6. Get Service Statistics
**GET** `/api/services/statistics`

Retrieves comprehensive service statistics for admin dashboard.

**Response:**
```javascript
{
  "success": true,
  "data": {
    "totalServices": 45,
    "serviceTypeStats": [
      {
        "_id": "Web Development",
        "count": 12
      },
      {
        "_id": "Event Management", 
        "count": 8
      }
    ],
    "categoryStats": [
      {
        "_id": "Technology",
        "count": 20
      },
      {
        "_id": "Events",
        "count": 15
      }
    ],
    "featuredCount": 8,
    "recentAdditions": 5,
    "mostViewed": [
      {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
        "title": "Web Development",
        "views": 1250
      }
    ],
    "totalViews": 15420
  }
}
```

---

## 🚨 **Error Responses**

### Validation Error (400)
```javascript
{
  "success": false,
  "message": "Validation error",
  "errors": [
    "Title is required",
    "Description 1 cannot exceed 1000 characters"
  ]
}
```

### Not Found (404)
```javascript
{
  "success": false,
  "message": "Service not found"
}
```

### Server Error (500)
```javascript
{
  "success": false,
  "message": "Failed to create service",
  "debug": "Detailed error message (development only)"
}
```

---

## 📝 **Usage Examples**

### Frontend Integration
```javascript
// Create service with SVG icon and image
const formData = new FormData();
formData.append('title', 'Web Development');
formData.append('description', 'Professional web development services');
formData.append('description2', 'Secondary description');
formData.append('howWeDoIt[title]', 'How We Do It');
formData.append('howWeDoIt[description]', 'Our process explanation');
formData.append('sortOrder', '1');
formData.append('icons', svgIconFile);
formData.append('image', serviceImageFile);

const response = await fetch('/api/services', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Get services with filtering
const services = await fetch('/api/services?search=web&page=1&limit=10', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### Admin Features
- ✅ Create unlimited services for different categories
- ✅ Upload SVG icons and service images
- ✅ Simple content management
- ✅ How We Do It sections
- ✅ Basic filtering and search
- ✅ Custom sorting order

---

## 🔧 **Technical Notes**

- **File Upload**: Uses AWS S3 for file storage
- **SVG Icons**: Supports SVG format for icons
- **Image Types**: Supports JPG, PNG, GIF, WebP for service images
- **File Size**: Maximum 5MB per file
- **Database**: MongoDB with ObjectId
- **Validation**: Joi schema validation
- **Security**: JWT authentication required
- **Soft Delete**: Services are never permanently deleted

---

*Last Updated: December 2024*
