Write-Host "Testing with Fresh Token"
Write-Host "========================"

# Get fresh token
Write-Host "Getting fresh token..."
$loginData = '{"email":"<EMAIL>","password":"password123"}'

try {
    $loginResult = Invoke-RestMethod -Uri 'http://localhost:8005/api/auth/login-quick' -Method POST -ContentType 'application/json' -Body $loginData
    $token = $loginResult.data.token
    Write-Host "SUCCESS: Token obtained"
    
    $headers = @{
        'Authorization' = "Bearer $token"
    }
    
    # Test Review Creation
    Write-Host "`nTesting Review Creation..."
    $reviewData = @{
        name = "Final Test User"
        review = "Testing after complete fixedId removal"
        star = 5
        relationship = "client"
        eventType = "wedding"
    } | ConvertTo-Json

    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData
    Write-Host "SUCCESS: Review created - ID: $($result.data._id)"
    
    # Test Second Review
    Write-Host "`nTesting Second Review..."
    $reviewData2 = @{
        name = "Another Final Test"
        review = "Second review - no duplicate error expected"
        star = 4
        relationship = "client"
        eventType = "corporate"
    } | ConvertTo-Json

    $result2 = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData2
    Write-Host "SUCCESS: Second review created - ID: $($result2.data._id)"
    
    Write-Host "`nEXCELLENT: No duplicate errors! Admin can create multiple reviews!"
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}

Write-Host "`nTest Complete!"
