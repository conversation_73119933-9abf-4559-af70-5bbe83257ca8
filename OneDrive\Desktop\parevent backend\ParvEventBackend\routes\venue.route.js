const express = require('express');
const router = express.Router();
const {
    createVenue,
    getAllVenues,
    getVenueById,
    updateVenue,
    deleteVenue
} = require('../controllers/venue.controller');
const { venueUploadSingle } = require('../config/awsclouds3');
const authMiddleware = require('../middleware/auth.middleware');

// Apply security headers to all venue routes
router.use(authMiddleware.securityHeaders);

// Public routes (no authentication required)
router.get('/', getAllVenues);
router.get('/:id', getVenueById);

// Protected routes (authentication required)
router.post('/',
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    venueUploadSingle('image'),
    createVenue
);

router.put('/:id',
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    venueUploadSingle('image'),
    updateVenue
);

router.delete('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    deleteVenue
);

module.exports = router;
