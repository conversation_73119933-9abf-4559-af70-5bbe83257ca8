const mongoose = require('mongoose');
require('dotenv').config();

async function removeFixedIdIndexes() {
    try {
        // Connect to database
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to database');

        const db = mongoose.connection.db;
        
        // List of collections that might have fixedId indexes
        const collections = [
            'reviews',
            'galleries', 
            'herosections',
            'teams',
            'venues',
            'services',
            'venueforms'
        ];

        console.log('🔍 Checking and removing fixedId indexes...');

        for (const collectionName of collections) {
            try {
                const collection = db.collection(collectionName);
                
                // Get all indexes for this collection
                const indexes = await collection.indexes();
                console.log(`\n📋 Collection: ${collectionName}`);
                console.log(`   Found ${indexes.length} indexes`);

                // Check each index for fixedId
                for (const index of indexes) {
                    const indexName = index.name;
                    const indexKeys = Object.keys(index.key);
                    
                    // If index contains fixedId, remove it
                    if (indexKeys.includes('fixedId') || indexName.includes('fixedId')) {
                        console.log(`   🗑️  Removing index: ${indexName}`);
                        try {
                            await collection.dropIndex(indexName);
                            console.log(`   ✅ Successfully removed index: ${indexName}`);
                        } catch (dropError) {
                            console.log(`   ⚠️  Could not remove index ${indexName}: ${dropError.message}`);
                        }
                    } else {
                        console.log(`   ✓ Keeping index: ${indexName}`);
                    }
                }

                // Also try to remove fixedId field from documents (set to null)
                const updateResult = await collection.updateMany(
                    { fixedId: { $exists: true } },
                    { $unset: { fixedId: "" } }
                );
                
                if (updateResult.modifiedCount > 0) {
                    console.log(`   🧹 Removed fixedId field from ${updateResult.modifiedCount} documents`);
                }

            } catch (collectionError) {
                console.log(`   ❌ Error processing collection ${collectionName}: ${collectionError.message}`);
            }
        }

        console.log('\n🎉 fixedId cleanup completed!');
        console.log('✅ All fixedId indexes removed');
        console.log('✅ All fixedId fields removed from documents');
        console.log('✅ Admin can now create unlimited items without duplicate errors');

        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

removeFixedIdIndexes();
