<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Icons Test - Fixed Version</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .service-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 50%;
            padding: 15px;
            border: 2px solid #e9ecef;
        }

        .service-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .service-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .icon-status {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .icon-fallback {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .loading {
            text-align: center;
            padding: 3rem;
        }

        .error {
            color: #dc3545;
            text-align: center;
            padding: 2rem;
        }

        .icon-test-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🔧 Service Icons Test - Fixed Version</h1>
                <p class="text-center text-muted mb-4">
                    Testing both SVG icons and regular image icons with fallback support
                </p>
            </div>
        </div>

        <div class="icon-test-info">
            <h5>🛠️ Icon System Features:</h5>
            <ul class="mb-0">
                <li><strong>SVG Icons:</strong> Primary option stored in <code>icons</code> field</li>
                <li><strong>Image Icons:</strong> Fallback option stored in <code>iconImage</code> field</li>
                <li><strong>Display Icon:</strong> Automatically uses the best available icon</li>
                <li><strong>Fallback Display:</strong> Shows service title initial if no icon available</li>
            </ul>
        </div>

        <div id="loading" class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading services...</span>
            </div>
            <p class="mt-2">Loading services with icon testing...</p>
        </div>

        <div id="error" class="error d-none">
            <div class="alert alert-danger">
                <h5>❌ Error Loading Services</h5>
                <p id="error-message">Failed to load services</p>
            </div>
        </div>

        <div id="services-container" class="row d-none">
            <!-- Services will be loaded here -->
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📋 Icon Testing Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>✅ SVG Icons (Primary)</h6>
                                <ul>
                                    <li>Field: <code>icons</code></li>
                                    <li>Format: SVG files</li>
                                    <li>Storage: <code>services/icons/</code></li>
                                    <li>Benefits: Scalable, lightweight</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>🖼️ Image Icons (Fallback)</h6>
                                <ul>
                                    <li>Field: <code>iconImage</code></li>
                                    <li>Format: PNG, JPG, GIF, WebP</li>
                                    <li>Storage: <code>services/icon-images/</code></li>
                                    <li>Benefits: Always displays properly</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API base URL
        const API_BASE = '';

        // Fetch services from API
        async function fetchServices() {
            try {
                const response = await fetch(`${API_BASE}/services?limit=20&sortBy=sortOrder&sortOrder=asc`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || 'Failed to fetch services');
                }
                
                return result.data.services;
            } catch (error) {
                console.error('Error fetching services:', error);
                throw error;
            }
        }

        // Test icon loading
        function testIconLoad(iconUrl, fallbackUrl, serviceTitle) {
            return new Promise((resolve) => {
                if (!iconUrl && !fallbackUrl) {
                    resolve({
                        success: false,
                        iconType: 'none',
                        displayIcon: null,
                        fallbackInitial: serviceTitle.charAt(0).toUpperCase()
                    });
                    return;
                }

                const primaryIcon = iconUrl || fallbackUrl;
                const img = new Image();
                
                img.onload = () => {
                    resolve({
                        success: true,
                        iconType: iconUrl ? 'svg' : 'image',
                        displayIcon: primaryIcon,
                        fallbackInitial: null
                    });
                };
                
                img.onerror = () => {
                    if (iconUrl && fallbackUrl && iconUrl !== fallbackUrl) {
                        // Try fallback icon
                        const fallbackImg = new Image();
                        fallbackImg.onload = () => {
                            resolve({
                                success: true,
                                iconType: 'image-fallback',
                                displayIcon: fallbackUrl,
                                fallbackInitial: null
                            });
                        };
                        fallbackImg.onerror = () => {
                            resolve({
                                success: false,
                                iconType: 'failed',
                                displayIcon: null,
                                fallbackInitial: serviceTitle.charAt(0).toUpperCase()
                            });
                        };
                        fallbackImg.src = fallbackUrl;
                    } else {
                        resolve({
                            success: false,
                            iconType: 'failed',
                            displayIcon: null,
                            fallbackInitial: serviceTitle.charAt(0).toUpperCase()
                        });
                    }
                };
                
                img.src = primaryIcon;
            });
        }

        // Display services with icon testing
        async function displayServices(services) {
            const container = document.getElementById('services-container');
            
            if (!services || services.length === 0) {
                container.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <h5>📝 No Services Found</h5>
                            <p>No services are currently available. Please add some services first.</p>
                        </div>
                    </div>
                `;
                return;
            }

            // Test each service icon
            const serviceCards = await Promise.all(services.map(async (service) => {
                const iconTest = await testIconLoad(service.icons, service.iconImage, service.title);
                
                let iconHtml;
                let statusHtml;
                
                if (iconTest.success) {
                    iconHtml = `<img src="${iconTest.displayIcon}" alt="${service.title} Icon">`;
                    statusHtml = `<div class="icon-status text-success">✅ ${iconTest.iconType.toUpperCase()} loaded</div>`;
                } else {
                    iconHtml = `<div class="icon-fallback">${iconTest.fallbackInitial}</div>`;
                    statusHtml = `<div class="icon-status text-danger">❌ Icon failed, showing fallback</div>`;
                }

                return `
                    <div class="col-md-4 col-sm-6 mb-4">
                        <div class="card service-card h-100">
                            <div class="card-body text-center">
                                <div class="service-icon mb-3">
                                    ${iconHtml}
                                </div>
                                ${statusHtml}
                                <h5 class="card-title">${service.title}</h5>
                                <p class="card-text">${service.description}</p>
                                <div class="mt-auto">
                                    <small class="text-muted">
                                        SVG: ${service.icons ? '✅' : '❌'} | 
                                        Image: ${service.iconImage ? '✅' : '❌'} | 
                                        Display: ${service.displayIcon ? '✅' : '❌'}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }));

            container.innerHTML = serviceCards.join('');
        }

        // Show error message
        function showError(message) {
            document.getElementById('loading').classList.add('d-none');
            document.getElementById('error').classList.remove('d-none');
            document.getElementById('error-message').textContent = message;
        }

        // Show services
        function showServices(services) {
            document.getElementById('loading').classList.add('d-none');
            document.getElementById('services-container').classList.remove('d-none');
            displayServices(services);
        }

        // Load services on page load
        async function loadServices() {
            try {
                console.log('🔄 Loading services with icon testing...');
                const services = await fetchServices();
                console.log(`✅ Loaded ${services.length} services`);
                
                // Log icon information for debugging
                services.forEach(service => {
                    console.log(`Service: ${service.title}`);
                    console.log(`  - SVG Icon: ${service.icons || 'None'}`);
                    console.log(`  - Image Icon: ${service.iconImage || 'None'}`);
                    console.log(`  - Display Icon: ${service.displayIcon || 'None'}`);
                });
                
                showServices(services);
            } catch (error) {
                console.error('❌ Error loading services:', error);
                showError(error.message);
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', loadServices);
    </script>
</body>
</html>
