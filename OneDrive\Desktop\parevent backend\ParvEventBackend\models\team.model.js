const mongoose = require("mongoose");
const AutoIncrement = require("mongoose-sequence")(mongoose);

const teamSchema = new mongoose.Schema(
  {
    id: { type: Number, unique: true },
    name: {
      type: String,
      required: [true, "Name is required"],
      trim: true,
      maxlength: [100, "Name cannot exceed 100 characters"]
    },
    image: {
      type: String,
      required: [true, "Image is required"]
    },
    isActive: {
      type: Boolean,
      default: true
    },

    sortOrder: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true
  }
);

// Index for better search performance
teamSchema.index({ name: 'text' });
teamSchema.index({ isActive: 1 });

teamSchema.index({ sortOrder: 1 });
teamSchema.index({ createdAt: -1 });



// Method to get public data
teamSchema.methods.getPublicData = function() {
    return {
        _id: this._id,
        id: this.id,
        name: this.name,
        image: this.image,

        sortOrder: this.sortOrder,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt
    };
};



teamSchema.plugin(AutoIncrement, { inc_field: "id", id: "team_id_counter" });

module.exports = mongoose.model("Team", teamSchema);
