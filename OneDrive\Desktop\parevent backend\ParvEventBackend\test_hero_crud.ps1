$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTU1ODgxLCJleHAiOjE3NTExNTk0ODF9.GPbgqGvLiAH_0CeNZnkMcomH47jaTMAssmL4Zmhiokw'
}

Write-Host "=== Testing Hero Section CRUD with ObjectId ==="

# Test 1: Create Hero Section (without image - should fail)
Write-Host "`n1. Testing Hero Section Creation (without image)..."
$heroData = @{
    title = "Test Hero Section"
    subtitle = "Test Subtitle"
    description = "Test Description"
    buttonText = "Click Me"
    buttonLink = "#test"
} | ConvertTo-Json

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/hero-section' -Method POST -Headers $headers -ContentType 'application/json' -Body $heroData
    Write-Host "UNEXPECTED SUCCESS: $($result.message)"
    $heroId = $result.data._id
} catch {
    Write-Host "EXPECTED ERROR (no image): $($_.Exception.Message)"
}

# Test 2: Get All Hero Sections
Write-Host "`n2. Testing Get All Hero Sections..."
try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/hero-section' -Headers $headers
    Write-Host "SUCCESS: Found $($result.data.heroSections.Count) hero sections"
    
    if ($result.data.heroSections.Count -gt 0) {
        $heroId = $result.data.heroSections[0]._id
        Write-Host "Using existing hero ID: $heroId"
        
        # Test 3: Get Hero by ID
        Write-Host "`n3. Testing Get Hero by ObjectId..."
        try {
            $getResult = Invoke-RestMethod -Uri "http://localhost:8005/api/hero-section/$heroId" -Headers $headers
            Write-Host "SUCCESS: Retrieved hero section with ObjectId"
            Write-Host "Title: $($getResult.data.title)"
        } catch {
            Write-Host "ERROR: $($_.Exception.Message)"
        }
        
        # Test 4: Update Hero Section
        Write-Host "`n4. Testing Update Hero Section..."
        $updateData = @{
            title = "Updated Hero Title"
            subtitle = "Updated Subtitle"
        } | ConvertTo-Json
        
        try {
            $updateResult = Invoke-RestMethod -Uri "http://localhost:8005/api/hero-section/$heroId" -Method PUT -Headers $headers -ContentType 'application/json' -Body $updateData
            Write-Host "SUCCESS: Hero section updated"
            Write-Host "New title: $($updateResult.data.title)"
        } catch {
            Write-Host "ERROR: $($_.Exception.Message)"
        }
        
        # Test 5: Delete Hero Section
        Write-Host "`n5. Testing Delete Hero Section..."
        try {
            $deleteResult = Invoke-RestMethod -Uri "http://localhost:8005/api/hero-section/$heroId" -Method DELETE -Headers $headers
            Write-Host "SUCCESS: Hero section deleted"
        } catch {
            Write-Host "ERROR: $($_.Exception.Message)"
        }
    } else {
        Write-Host "No existing hero sections found to test with"
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}

Write-Host "`n=== Hero Section CRUD Test Complete ==="
