const mongoose = require('mongoose');

const gallerySchema = new mongoose.Schema({
    title: {
        type: String,
        required: [true, 'Title is required'],
        trim: true,
        maxlength: [100, 'Title cannot exceed 100 characters']
    },
    description: {
        type: String,
        trim: true,
        maxlength: [500, 'Description cannot exceed 500 characters']
    },
    image: {
        type: String,
        required: [true, 'Image is required']
    },
    keywords: [{
        type: String,
        trim: true,
        lowercase: true
    }],
    category: {
        type: String,
        enum: ['wedding', 'corporate', 'birthday', 'anniversary', 'other'],
        default: 'other'
    },
    isActive: {
        type: Boolean,
        default: true
    },

    sortOrder: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});

// Index for better performance
gallerySchema.index({ category: 1, isActive: 1 });
gallerySchema.index({ keywords: 1 });

gallerySchema.index({ sortOrder: 1 });



// Method to get public data
gallerySchema.methods.getPublicData = function() {
    return {
        _id: this._id,
        title: this.title,
        description: this.description,
        image: this.image,
        keywords: this.keywords,
        category: this.category,

        sortOrder: this.sortOrder,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt
    };
};



module.exports = mongoose.model('Gallery', gallerySchema);
