const Service = require("../models/service.model");
const { s3Client } = require("../config/awsclouds3");
const { DeleteObjectCommand } = require("@aws-sdk/client-s3");

// Create a new service
const createService = async (serviceData, files) => {
  try {
    const {
      title,
      description,
      serviceDescription,
      importantPoints,
      questionsAnswers,
      category,
      tags,
      status,
      featured,
      iconBgColor
    } = serviceData;

    // Validate required fields
    if (!title || !description || !serviceDescription) {
      throw new Error("Title, description, and service description are required");
    }

    // Prepare service data
    const newServiceData = {
      title: title.trim(),
      description: description.trim(),
      serviceDescription: serviceDescription.trim(),
      category: category || null,
      status: status || "active",
      featured: featured === 'true' || featured === true || false,
      iconBgColor: iconBgColor || "#ffffff"
    };

    // Handle important points
    if (importantPoints) {
      const parsedPoints = typeof importantPoints === 'string' 
        ? JSON.parse(importantPoints) 
        : importantPoints;
      newServiceData.importantPoints = Array.isArray(parsedPoints) 
        ? parsedPoints.filter(point => point && point.trim()) 
        : [];
    }



    // Handle questions and answers
    if (questionsAnswers) {
      const parsedQA = typeof questionsAnswers === 'string' 
        ? JSON.parse(questionsAnswers) 
        : questionsAnswers;
      newServiceData.questionsAnswers = Array.isArray(parsedQA) 
        ? parsedQA.map((qa, index) => ({
            question: qa.question,
            answer: qa.answer,
            order: qa.order || index
          }))
        : [];
    }

    // Handle tags
    if (tags) {
      const parsedTags = typeof tags === 'string' 
        ? tags.split(',').map(tag => tag.trim().toLowerCase()).filter(t => t)
        : Array.isArray(tags) 
        ? tags.map(tag => tag.trim().toLowerCase()).filter(t => t)
        : [];
      newServiceData.tags = parsedTags;
    }

    // Handle file uploads
    if (files) {
      if (files.icon && files.icon[0]) {
        newServiceData.iconImageUrl = files.icon[0].location;
      }
      if (files.video && files.video[0]) {
        newServiceData.videoUrl = files.video[0].location;
      }
    }

    // Create and save service
    const service = new Service(newServiceData);
    const savedService = await service.save();

    return savedService;
  } catch (error) {
    // If there was an error and files were uploaded, clean them up
    if (files) {
      if (files.icon && files.icon[0] && files.icon[0].key) {
        await deleteFileFromS3(files.icon[0].key);
      }
      if (files.video && files.video[0] && files.video[0].key) {
        await deleteFileFromS3(files.video[0].key);
      }
    }
    throw error;
  }
};

// Get all services with filtering and pagination
const getAllServices = async (queryParams = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      status = "active",
      search,
      featured,
      tags,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = queryParams;

    // Build filter object
    const filter = { status };

    if (category) {
      filter.category = category;
    }

    if (featured !== undefined) {
      filter.featured = featured === 'true' || featured === true;
    }

    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
        { serviceDescription: { $regex: search, $options: "i" } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    if (tags) {
      const tagArray = typeof tags === 'string' ? tags.split(',').map(t => t.trim()) : tags;
      filter.tags = { $in: tagArray.map(t => new RegExp(t, 'i')) };
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query with pagination
    const services = await Service.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalCount = await Service.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    return {
      services,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

// Get service by ID (supports both MongoDB ObjectId and auto-increment id)
const getServiceById = async (id) => {
  try {
    let service;

    // Check if it's a valid MongoDB ObjectId (24 character hex string)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      service = await Service.findById(id);

      // Increment view count if service found
      if (service) {
        await Service.findByIdAndUpdate(id, { $inc: { views: 1 } });
      }
    } else {
      // Use auto-increment id (numeric)
      const numericId = parseInt(id);
      if (!isNaN(numericId)) {
        service = await Service.findOne({ id: numericId });

        // Increment view count if service found
        if (service) {
          await Service.findOneAndUpdate(
            { id: numericId },
            { $inc: { views: 1 } }
          );
        }
      }
    }

    return service;
  } catch (error) {
    throw error;
  }
};

// Update service (supports both MongoDB ObjectId and auto-increment id)
const updateService = async (id, updateData, files) => {
  try {
    let existingService;

    // Check if it's a valid MongoDB ObjectId (24 character hex string)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      existingService = await Service.findById(id);
    } else {
      const numericId = parseInt(id);
      if (!isNaN(numericId)) {
        existingService = await Service.findOne({ id: numericId });
      }
    }

    if (!existingService) {
      throw new Error("Service not found");
    }

    // Prepare update data
    const updatedData = { ...updateData };

    // Handle important points
    if (updateData.importantPoints) {
      const parsedPoints = typeof updateData.importantPoints === 'string' 
        ? JSON.parse(updateData.importantPoints) 
        : updateData.importantPoints;
      updatedData.importantPoints = Array.isArray(parsedPoints) 
        ? parsedPoints.filter(point => point && point.trim()) 
        : [];
    }



    // Handle questions and answers
    if (updateData.questionsAnswers) {
      const parsedQA = typeof updateData.questionsAnswers === 'string' 
        ? JSON.parse(updateData.questionsAnswers) 
        : updateData.questionsAnswers;
      updatedData.questionsAnswers = Array.isArray(parsedQA) 
        ? parsedQA.map((qa, index) => ({
            question: qa.question,
            answer: qa.answer,
            order: qa.order || index
          }))
        : [];
    }

    // Handle tags
    if (updateData.tags) {
      const parsedTags = typeof updateData.tags === 'string' 
        ? updateData.tags.split(',').map(tag => tag.trim().toLowerCase()).filter(t => t)
        : Array.isArray(updateData.tags) 
        ? updateData.tags.map(tag => tag.trim().toLowerCase()).filter(t => t)
        : [];
      updatedData.tags = parsedTags;
    }

    // Handle featured field
    if (updateData.featured !== undefined) {
      updatedData.featured = updateData.featured === 'true' || updateData.featured === true;
    }

    // Handle iconBgColor field
    if (updateData.iconBgColor !== undefined) {
      updatedData.iconBgColor = updateData.iconBgColor || "#ffffff";
    }

    // Handle file updates
    if (files) {
      // Handle icon update
      if (files.icon && files.icon[0]) {
        // Delete old icon if exists
        if (existingService.iconImageUrl) {
          const oldIconKey = extractS3KeyFromUrl(existingService.iconImageUrl);
          if (oldIconKey) {
            await deleteFileFromS3(oldIconKey);
          }
        }
        updatedData.iconImageUrl = files.icon[0].location;
      }

      // Handle video update
      if (files.video && files.video[0]) {
        // Delete old video if exists
        if (existingService.videoUrl) {
          const oldVideoKey = extractS3KeyFromUrl(existingService.videoUrl);
          if (oldVideoKey) {
            await deleteFileFromS3(oldVideoKey);
          }
        }
        updatedData.videoUrl = files.video[0].location;
      }
    }

    // Update service
    let updatedService;
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      updatedService = await Service.findByIdAndUpdate(
        id,
        updatedData,
        { new: true, runValidators: true }
      );
    } else {
      // Use auto-increment id
      updatedService = await Service.findOneAndUpdate(
        { id: parseInt(id) },
        updatedData,
        { new: true, runValidators: true }
      );
    }

    return updatedService;
  } catch (error) {
    // If there was an error and new files were uploaded, clean them up
    if (files) {
      if (files.icon && files.icon[0] && files.icon[0].key) {
        await deleteFileFromS3(files.icon[0].key);
      }
      if (files.video && files.video[0] && files.video[0].key) {
        await deleteFileFromS3(files.video[0].key);
      }
    }
    throw error;
  }
};

// Delete service (supports both MongoDB ObjectId and auto-increment id)
const deleteService = async (id) => {
  try {
    let service;

    // Check if it's a valid MongoDB ObjectId (24 character hex string)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      service = await Service.findById(id);
    } else {
      const numericId = parseInt(id);
      if (!isNaN(numericId)) {
        service = await Service.findOne({ id: numericId });
      }
    }

    if (!service) {
      throw new Error("Service not found");
    }

    // Delete files from S3 if they exist
    if (service.iconImageUrl) {
      const iconKey = extractS3KeyFromUrl(service.iconImageUrl);
      if (iconKey) {
        await deleteFileFromS3(iconKey);
      }
    }

    if (service.videoUrl) {
      const videoKey = extractS3KeyFromUrl(service.videoUrl);
      if (videoKey) {
        await deleteFileFromS3(videoKey);
      }
    }

    // Delete service from database
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      await Service.findByIdAndDelete(id);
    } else {
      await Service.findOneAndDelete({ id: parseInt(id) });
    }

    return { message: "Service deleted successfully" };
  } catch (error) {
    throw error;
  }
};

// Get services by category
const getServicesByCategory = async (category, queryParams = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      status = "active",
      search,
      featured,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = queryParams;

    // Build filter object
    const filter = { category, status };

    if (featured !== undefined) {
      filter.featured = featured === 'true' || featured === true;
    }

    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
        { serviceDescription: { $regex: search, $options: "i" } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query
    const services = await Service.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count
    const totalCount = await Service.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    return {
      services,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

// Get featured services
const getFeaturedServices = async (queryParams = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      status = "active",
      category,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = queryParams;

    // Build filter object
    const filter = { featured: true, status };

    if (category) {
      filter.category = category;
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query
    const services = await Service.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count
    const totalCount = await Service.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    return {
      services,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

// Helper function to extract S3 key from URL
const extractS3KeyFromUrl = (fileUrl) => {
  try {
    if (!fileUrl) return null;

    // Extract key from S3 URL
    // URL format: https://bucket-name.s3.region.amazonaws.com/services/folder/filename.ext
    const url = new URL(fileUrl);
    const key = url.pathname.substring(1); // Remove leading slash
    return key;
  } catch (error) {
    console.error('Error extracting S3 key from URL:', error);
    return null;
  }
};

// Helper function to delete file from S3
const deleteFileFromS3 = async (fileKey) => {
  try {
    if (!fileKey) return;

    const deleteParams = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: fileKey
    };

    const command = new DeleteObjectCommand(deleteParams);
    await s3Client.send(command);
    console.log(`✅ File deleted from S3: ${fileKey}`);
  } catch (error) {
    console.error(`❌ Error deleting file from S3: ${fileKey}`, error);
    // Don't throw error here as it shouldn't stop the main operation
  }
};

// Get service statistics
const getServiceStatistics = async () => {
  try {
    const totalServices = await Service.countDocuments();
    const activeServices = await Service.countDocuments({ status: "active" });
    const inactiveServices = await Service.countDocuments({ status: "inactive" });
    const draftServices = await Service.countDocuments({ status: "draft" });
    const featuredServices = await Service.countDocuments({ featured: true });

    // Get services by category
    const servicesByCategory = await Service.aggregate([
      {
        $group: {
          _id: "$category",
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get most viewed services
    const mostViewedServices = await Service.find({ status: "active" })
      .sort({ views: -1 })
      .limit(5)
      .select('title views category');

    return {
      totalServices,
      activeServices,
      inactiveServices,
      draftServices,
      featuredServices,
      servicesByCategory,
      mostViewedServices
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createService,
  getAllServices,
  getServiceById,
  updateService,
  deleteService,
  getServicesByCategory,
  getFeaturedServices,
  getServiceStatistics
};
