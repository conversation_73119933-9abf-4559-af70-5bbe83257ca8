const express = require('express');
const router = express.Router();
const contactFormRoute = require('./contactForm.route');
const contactRoute = require('./contact.route');
const uploadRoute = require('./upload.route');
const blogRoute = require('./blog.route');
const teamRoute = require('./team.route');
const serviceRoute = require('./service.route');
const authRoute = require('./auth.route');
const faqRoute = require('./faq.route');
const postCommentRoute = require('./postComment.route');
const galleryRoute = require('./gallery.route');
const reviewRoute = require('./review.route');
const heroSectionRoute = require('./heroSection.route');
const venueRoute = require('./venue.route');
const venueFormRoute = require('./venueForm.route');

// Route for authentication (admin signup/login)
router.use('/auth', authRoute);

// Route for contact form
router.use('/contact-form', contactFormRoute);

// Route for contact management (new CRUD API)
router.use('/', contactRoute);

// New API Routes
router.use('/gallery', galleryRoute);
router.use('/reviews', reviewRoute);
router.use('/hero-section', heroSectionRoute);
router.use('/venues', venueRoute);
router.use('/venue-bookings', venueFormRoute);
router.use('/team', teamRoute);
router.use('/services', serviceRoute);

// Route for file uploads
router.use('/upload', uploadRoute);

// Route for blog management
router.use('/', blogRoute);

// Route for FAQ management
router.use('/', faqRoute);

// Route for post comment management
router.use('/', postCommentRoute);

module.exports = router;