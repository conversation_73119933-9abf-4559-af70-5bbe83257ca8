const express = require("express");
const {
  createPostComment,
  getAllPostComments,
  getPostCommentById,
  updatePostComment,
  deletePostComment,
  getApprovedComments,
  getCommentsByBlogId,
  getCommentCountByBlogId,
  getCommentStatistics
} = require("../controllers/postComment.controller");
const router = express.Router();

// Post Comment CRUD routes

// POST - Create new comment (Public)
router.post("/comments", createPostComment);

// GET - Get approved comments (Public)
router.get("/comments/approved", getApprovedComments);

// GET - Get comments by blog ID (Public)
router.get("/comments/blog/:blogId", getCommentsByBlogId);

// GET - Get comment count by blog ID (Public)
router.get("/comments/blog/:blogId/count", getCommentCountByBlogId);

// GET - Get all comments with filtering and pagination (Admin)
router.get("/comments", getAllPostComments);

// GET - Get comment statistics (Admin)
router.get("/comments/statistics", getCommentStatistics);

// GET - Get comment by ID (Admin)
router.get("/comments/:id", getPostCommentById);

// PUT - Update comment by ID (Admin - for moderation)
router.put("/comments/:id", updatePostComment);

// DELETE - Delete comment by ID (Admin)
router.delete("/comments/:id", deletePostComment);

module.exports = router;
