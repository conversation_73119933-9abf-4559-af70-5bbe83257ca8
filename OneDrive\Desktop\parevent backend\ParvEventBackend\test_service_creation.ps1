Write-Host "Testing Service Creation with New Format"
Write-Host "========================================="

# Get fresh token
Write-Host "Getting fresh token..."
$loginData = '{"email":"<EMAIL>","password":"password123"}'

try {
    $loginResult = Invoke-RestMethod -Uri 'http://localhost:8005/api/auth/login-quick' -Method POST -ContentType 'application/json' -Body $loginData
    $token = $loginResult.data.token
    Write-Host "SUCCESS: Token obtained"
    
    $headers = @{
        'Authorization' = "Bearer $token"
    }
    
    # Test Service Creation with howWeDoIt array
    Write-Host "`nTesting Service Creation..."
    
    # Create form data
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"
    
    $bodyLines = @(
        "--$boundary",
        'Content-Disposition: form-data; name="title"',
        '',
        'Web Development Service',
        "--$boundary",
        'Content-Disposition: form-data; name="description"',
        '',
        'Professional web development services with modern technologies',
        "--$boundary",
        'Content-Disposition: form-data; name="description2"',
        '',
        'We create responsive and user-friendly websites',
        "--$boundary",
        'Content-Disposition: form-data; name="howWeDoIt[0][title]"',
        '',
        'Step 1: Planning',
        "--$boundary",
        'Content-Disposition: form-data; name="howWeDoIt[0][description]"',
        '',
        'We start by understanding your requirements and planning the project',
        "--$boundary",
        'Content-Disposition: form-data; name="howWeDoIt[1][title]"',
        '',
        'Step 2: Development',
        "--$boundary",
        'Content-Disposition: form-data; name="howWeDoIt[1][description]"',
        '',
        'We develop your website using latest technologies and best practices',
        "--$boundary",
        'Content-Disposition: form-data; name="sortOrder"',
        '',
        '1',
        "--$boundary--"
    )
    
    $body = $bodyLines -join $LF
    
    Write-Host "Form data prepared (without files for testing)"
    Write-Host "Note: This will fail because icons and image files are required"
    Write-Host "But it will test the validation and field structure"
    
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/services' -Method POST -Headers $headers -ContentType "multipart/form-data; boundary=$boundary" -Body $body
    Write-Host "UNEXPECTED: Service created without files"
    
} catch {
    $errorMessage = $_.Exception.Message
    if ($errorMessage -like "*SVG icon and service image are required*") {
        Write-Host "✅ EXPECTED: Validation working - files are required"
    } elseif ($errorMessage -like "*Invalid field name*") {
        Write-Host "❌ FIELD ERROR: Still field name issues"
    } else {
        Write-Host "❌ OTHER ERROR: $errorMessage"
    }
}

Write-Host "`nTest Complete!"
Write-Host "`nTo test with actual files, use a proper form with:"
Write-Host "- icons: SVG file"
Write-Host "- image: JPG/PNG file"
Write-Host "- howWeDoIt: Array format for accordion"
