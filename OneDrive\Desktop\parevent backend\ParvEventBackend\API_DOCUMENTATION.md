# ParvEvent Backend API Documentation

## Base URL
```
http://localhost:8005/api
```

## Authentication
Most endpoints require admin authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Fixed ObjectIDs
All entities use fixed ObjectIDs for frontend routing. These are unique, predictable identifiers that can be used in URLs.

Format: `{entity}_{timestamp}_{random}`
- Gallery: `gallery_1703123456789_abc123def`
- Review: `review_1703123456789_xyz789ghi`
- Hero Section: `hero_1703123456789_mno456pqr`
- Venue: `venue_1703123456789_stu123vwx`
- Service: `service_1703123456789_def456ghi`
- Team: `team_1703123456789_jkl789mno`

---

## 1. Gallery APIs

### 1.1 Create Gallery Item
**POST** `/gallery`
- **Auth Required**: Yes
- **Content-Type**: `multipart/form-data`

**Request Body:**
```json
{
  "title": "Wedding Gallery",
  "description": "Beautiful wedding moments",
  "keywords": ["wedding", "photography", "celebration"],
  "category": "wedding",
  "fixedId": "gallery_custom_id", // Optional
  "sortOrder": 1
}
```

**Files:**
- `image` (required): Gallery image file

**Response:**
```json
{
  "success": true,
  "message": "Gallery item created successfully",
  "data": {
    "_id": "64f...",
    "title": "Wedding Gallery",
    "description": "Beautiful wedding moments",
    "image": "https://s3.amazonaws.com/bucket/gallery/image.jpg",
    "keywords": ["wedding", "photography", "celebration"],
    "category": "wedding",
    "fixedId": "gallery_1703123456789_abc123def",
    "sortOrder": 1,
    "createdAt": "2023-12-21T10:30:00.000Z",
    "updatedAt": "2023-12-21T10:30:00.000Z"
  }
}
```

### 1.2 Get All Gallery Items
**GET** `/gallery`
- **Auth Required**: No

**Query Parameters:**
- `page` (default: 1): Page number
- `limit` (default: 10): Items per page
- `category`: Filter by category (wedding, corporate, birthday, anniversary, other)
- `keyword`: Search by keyword
- `sortBy` (default: sortOrder): Sort field
- `sortOrder` (default: asc): Sort direction (asc/desc)

**Response:**
```json
{
  "success": true,
  "data": {
    "galleries": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

### 1.3 Get Gallery by ID
**GET** `/gallery/:id`
- **Auth Required**: No
- **ID**: MongoDB ObjectID or fixedId

### 1.4 Update Gallery Item
**PUT** `/gallery/:id`
- **Auth Required**: Yes
- **Content-Type**: `multipart/form-data`

### 1.5 Delete Gallery Item
**DELETE** `/gallery/:id`
- **Auth Required**: Yes

---

## 2. Review APIs

### 2.1 Create Review
**POST** `/reviews`
- **Auth Required**: Yes
- **Content-Type**: `multipart/form-data`

**Request Body:**
```json
{
  "name": "John Doe",
  "relationship": "groom",
  "review": "Amazing service! Highly recommended.",
  "star": 5,
  "eventType": "wedding",
  "isFeatured": false,
  "sortOrder": 1
}
```

**Files:**
- `image` (optional): Reviewer's image

**Response:**
```json
{
  "success": true,
  "message": "Review created successfully",
  "data": {
    "_id": "64f...",
    "name": "John Doe",
    "relationship": "groom",
    "review": "Amazing service! Highly recommended.",
    "star": 5,
    "ratingText": "Excellent",
    "image": "https://s3.amazonaws.com/bucket/reviews/image.jpg",
    "isFeatured": false,
    "fixedId": "review_1703123456789_xyz789ghi",
    "sortOrder": 1,
    "eventType": "wedding",
    "createdAt": "2023-12-21T10:30:00.000Z",
    "updatedAt": "2023-12-21T10:30:00.000Z"
  }
}
```

### 2.2 Get All Reviews
**GET** `/reviews`
- **Auth Required**: No

**Query Parameters:**
- `page`, `limit`: Pagination
- `star`: Filter by star rating (1-5)
- `relationship`: Filter by relationship
- `eventType`: Filter by event type
- `featured`: Filter featured reviews (true/false)
- `sortBy`, `sortOrder`: Sorting

### 2.3 Get Review Statistics
**GET** `/reviews/stats`
- **Auth Required**: No

**Response:**
```json
{
  "success": true,
  "data": {
    "averageRating": 4.5,
    "totalReviews": 150,
    "ratingDistribution": {
      "1": 2,
      "2": 5,
      "3": 15,
      "4": 45,
      "5": 83
    }
  }
}
```

### 2.4 Get Review by ID
**GET** `/reviews/:id`

### 2.5 Update Review
**PUT** `/reviews/:id`
- **Auth Required**: Yes

### 2.6 Delete Review
**DELETE** `/reviews/:id`
- **Auth Required**: Yes

---

## 3. Homepage Hero Section APIs

### 3.1 Create Hero Section
**POST** `/hero-section`
- **Auth Required**: Yes
- **Content-Type**: `multipart/form-data`

**Request Body:**
```json
{
  "title": "Welcome to ParvEvent",
  "subtitle": "Creating Memorable Moments",
  "description": "We specialize in making your special day unforgettable",
  "buttonText": "Get Started",
  "buttonLink": "/contact",
  "isPrimary": true,
  "sortOrder": 1,
  "displayDuration": 5000
}
```

**Files:**
- `image` (required): Hero section background image

### 3.2 Get All Hero Sections
**GET** `/hero-section`

### 3.3 Get Primary Hero Section
**GET** `/hero-section/primary`
- **Auth Required**: No

### 3.4 Get Hero Section by ID
**GET** `/hero-section/:id`

### 3.5 Update Hero Section
**PUT** `/hero-section/:id`
- **Auth Required**: Yes

### 3.6 Delete Hero Section
**DELETE** `/hero-section/:id`
- **Auth Required**: Yes

---

## 4. Venue APIs

### 4.1 Create Venue
**POST** `/venues`
- **Auth Required**: Yes
- **Content-Type**: `multipart/form-data`

**Request Body:**
```json
{
  "title": "Grand Ballroom",
  "description": "Elegant venue for weddings and events",
  "location": {
    "address": "123 Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400001",
    "coordinates": {
      "latitude": 19.0760,
      "longitude": 72.8777
    }
  },
  "venueType": "banquet-hall",
  "totalSeats": 200,
  "capacityTotal": 300,
  "price": {
    "basePrice": 50000,
    "currency": "INR",
    "priceType": "per-day",
    "additionalCharges": {
      "decoration": 10000,
      "catering": 500,
      "parking": 2000,
      "security": 5000
    }
  },
  "amenities": ["parking", "ac", "wifi", "catering", "decoration"],
  "isFeatured": false,
  "sortOrder": 1
}
```

**Files:**
- `image` (required): Main venue image
- `images` (optional): Additional venue images (max 10)

### 4.2 Get All Venues
**GET** `/venues`

**Query Parameters:**
- `page`, `limit`: Pagination
- `venueType`: Filter by venue type
- `city`: Filter by city
- `minPrice`, `maxPrice`: Price range filter
- `minCapacity`, `maxCapacity`: Capacity range filter
- `amenities`: Filter by amenities (comma-separated)
- `featured`: Filter featured venues

### 4.3 Get Venue by ID
**GET** `/venues/:id`

### 4.4 Update Venue
**PUT** `/venues/:id`
- **Auth Required**: Yes

### 4.5 Delete Venue
**DELETE** `/venues/:id`
- **Auth Required**: Yes

---

## 5. Venue Booking Form APIs

### 5.1 Submit Venue Booking
**POST** `/venue-bookings`
- **Auth Required**: No (Public form)

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "9876543210",
  "address": {
    "street": "456 Oak Avenue",
    "city": "Delhi",
    "state": "Delhi",
    "pincode": "110001"
  },
  "eventDate": "2024-06-15T00:00:00.000Z",
  "eventEndDate": "2024-06-16T00:00:00.000Z",
  "eventType": "wedding",
  "venueId": "venue_1703123456789_stu123vwx",
  "venueName": "Grand Ballroom",
  "guestCount": 250,
  "budget": {
    "min": 100000,
    "max": 200000,
    "currency": "INR"
  },
  "requirements": {
    "catering": true,
    "decoration": true,
    "photography": false,
    "music": true,
    "parking": true
  },
  "specialRequests": "Need vegetarian catering only",
  "source": "website"
}
```

### 5.2 Get All Venue Bookings (Admin)
**GET** `/venue-bookings`
- **Auth Required**: Yes

**Query Parameters:**
- `page`, `limit`: Pagination
- `status`: Filter by status (pending, confirmed, cancelled, completed)
- `priority`: Filter by priority (low, medium, high, urgent)
- `eventType`: Filter by event type
- `venueId`: Filter by venue
- `startDate`, `endDate`: Date range filter

### 5.3 Get Upcoming Events (Admin)
**GET** `/venue-bookings/upcoming`
- **Auth Required**: Yes

**Query Parameters:**
- `days` (default: 30): Number of days to look ahead

### 5.4 Get Venue Booking by ID (Admin)
**GET** `/venue-bookings/:id`
- **Auth Required**: Yes

### 5.5 Update Venue Booking (Admin)
**PUT** `/venue-bookings/:id`
- **Auth Required**: Yes

**Request Body:**
```json
{
  "status": "confirmed",
  "priority": "high",
  "adminNotes": "Confirmed booking with special arrangements",
  "followUpDate": "2024-01-15T10:00:00.000Z",
  "estimatedCost": 150000
}
```

### 5.6 Delete Venue Booking (Admin)
**DELETE** `/venue-bookings/:id`
- **Auth Required**: Yes

---

## 6. Services APIs

### 6.1 Create Service
**POST** `/services`
- **Auth Required**: Yes
- **Content-Type**: `multipart/form-data`

**Request Body:**
```json
{
  "title": "Wedding Photography",
  "description1": "Professional wedding photography services",
  "description2": "Capturing your special moments with artistic flair",
  "howWeDoIt": "Our team uses state-of-the-art equipment and creative techniques",
  "category": {
    "title": "Photography Services",
    "description": "Complete photography solutions for all events"
  },
  "serviceType": "Photography",
  "importantPoints": [
    "Professional equipment",
    "Experienced photographers",
    "Same-day editing"
  ],
  "questionsAnswers": [
    {
      "question": "How many photos do we get?",
      "answer": "You'll receive 200-300 edited photos",
      "order": 1
    }
  ],
  "tags": ["wedding", "photography", "professional"],
  "featured": false,
  "sortOrder": 1,
  "pricing": {
    "startingPrice": 25000,
    "currency": "INR",
    "priceType": "project-based"
  }
}
```

**Files:**
- `icons` (optional): Service icon/logo
- `serviceImage` (optional): Service showcase image

### 6.2 Get All Services
**GET** `/services`

**Query Parameters:**
- `page`, `limit`: Pagination
- `serviceType`: Filter by service type
- `category`: Filter by category title
- `featured`: Filter featured services
- `status`: Filter by status (active, inactive, draft)
- `search`: Search in title, descriptions, and tags

### 6.3 Get Featured Services
**GET** `/services/featured`

### 6.4 Get Services by Category
**GET** `/services/category/:category`

### 6.5 Get Service Statistics
**GET** `/services/statistics`

**Response:**
```json
{
  "success": true,
  "data": {
    "totalServices": 25,
    "serviceTypeStats": [
      {"_id": "Photography", "count": 5},
      {"_id": "Catering", "count": 8}
    ],
    "categoryStats": [
      {"_id": "Wedding Services", "count": 15},
      {"_id": "Corporate Events", "count": 10}
    ],
    "featuredCount": 8,
    "recentAdditions": 3,
    "totalViews": 1250
  }
}
```

### 6.6 Get Service by ID
**GET** `/services/:id`

### 6.7 Update Service
**PUT** `/services/:id`
- **Auth Required**: Yes

### 6.8 Delete Service
**DELETE** `/services/:id`
- **Auth Required**: Yes

---

## 7. Team APIs

### 7.1 Create Team Member
**POST** `/team`
- **Auth Required**: Yes
- **Content-Type**: `multipart/form-data`

**Request Body:**
```json
{
  "name": "John Smith",
  "sortOrder": 1
}
```

**Files:**
- `image` (required): Team member photo

**Response:**
```json
{
  "success": true,
  "message": "Team member created successfully",
  "data": {
    "_id": "64f...",
    "id": 1,
    "name": "John Smith",
    "image": "https://s3.amazonaws.com/bucket/team/image.jpg",
    "fixedId": "team_1703123456789_jkl789mno",
    "sortOrder": 1,
    "createdAt": "2023-12-21T10:30:00.000Z",
    "updatedAt": "2023-12-21T10:30:00.000Z"
  }
}
```

### 7.2 Get All Team Members
**GET** `/team`

**Query Parameters:**
- `page`, `limit`: Pagination
- `search`: Search by name
- `sortBy` (default: sortOrder): Sort field
- `sortOrder` (default: asc): Sort direction

### 7.3 Get Team Member by ID
**GET** `/team/:id`

### 7.4 Update Team Member
**PUT** `/team/:id`
- **Auth Required**: Yes
- **Content-Type**: `multipart/form-data`

### 7.5 Delete Team Member
**DELETE** `/team/:id`
- **Auth Required**: Yes

---

## 8. Authentication APIs

### 8.1 Admin Signup
**POST** `/auth/signup`

**Request Body:**
```json
{
  "firstname": "John",
  "lastname": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmpassword": "password123"
}
```

### 8.2 Test Signup (No Database)
**POST** `/auth/test-signup`
- Same as signup but doesn't save to database

### 8.3 Email Verification
**POST** `/auth/verify-email`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

### 8.4 2FA Verification
**POST** `/auth/verify-2fa`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "token": "123456"
}
```

### 8.5 Login
**POST** `/auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "token": "123456"
}
```

### 8.6 Logout
**POST** `/auth/logout`
- **Auth Required**: Yes

### 8.7 Get Profile
**GET** `/auth/profile`
- **Auth Required**: Yes

---

## Frontend Integration Examples

### React/Next.js Example
```javascript
// Fetch gallery items
const fetchGallery = async () => {
  try {
    const response = await fetch('http://localhost:8005/api/gallery?page=1&limit=12');
    const data = await response.json();
    
    if (data.success) {
      setGalleryItems(data.data.galleries);
    }
  } catch (error) {
    console.error('Error fetching gallery:', error);
  }
};

// Navigate to gallery item detail page using fixedId
const navigateToGalleryItem = (fixedId) => {
  router.push(`/gallery/${fixedId}`);
};

// Submit venue booking form
const submitVenueBooking = async (formData) => {
  try {
    const response = await fetch('http://localhost:8005/api/venue-bookings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData)
    });
    
    const data = await response.json();
    
    if (data.success) {
      alert('Booking submitted successfully!');
      // Redirect to thank you page
      router.push('/thank-you');
    }
  } catch (error) {
    console.error('Error submitting booking:', error);
  }
};
```

### Vue.js Example
```javascript
// Fetch services with fixed IDs for routing
async fetchServices() {
  try {
    const response = await this.$http.get('/api/services');
    this.services = response.data.data.services;
  } catch (error) {
    console.error('Error fetching services:', error);
  }
},

// Navigate to service detail using fixedId
viewServiceDetail(service) {
  this.$router.push(`/services/${service.fixedId}`);
}
```

---

## Error Handling

All APIs return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"],
  "debug": "Stack trace (development only)"
}
```

### Common HTTP Status Codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict (duplicate data)
- `500`: Internal Server Error

---

## Rate Limiting

- Auth endpoints: 5 requests per minute
- Public endpoints: No rate limiting in development
- File uploads: 10MB limit for most images, 15MB for hero sections

---

## File Upload Guidelines

### Supported Formats:
- Images: JPEG, PNG, GIF, WebP
- Max file sizes vary by endpoint

### S3 Folder Structure:
```
bucket/
├── gallery/          # Gallery images
├── reviews/          # Review images  
├── hero-section/     # Hero section images
├── venues/           # Venue images
├── services/         # Service images and icons
└── team/             # Team member images
```

---

## Testing with Postman

Import the provided Postman collection for easy API testing. All endpoints are pre-configured with proper headers and example data.

**Collection includes:**
- Authentication flows
- CRUD operations for all entities
- File upload examples
- Error scenarios
- Frontend integration examples
