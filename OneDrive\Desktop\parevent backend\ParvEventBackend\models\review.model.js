const mongoose = require('mongoose');

const reviewSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Name is required'],
        trim: true,
        maxlength: [100, 'Name cannot exceed 100 characters']
    },
    relationship: {
        type: String,
        required: [true, 'Relationship is required'],
        trim: true,
        maxlength: [100, 'Relationship cannot exceed 100 characters'],
        enum: ['bride', 'groom', 'parent', 'friend', 'relative', 'colleague', 'client', 'other']
    },
    review: {
        type: String,
        required: [true, 'Review text is required'],
        trim: true,
        maxlength: [1000, 'Review cannot exceed 1000 characters']
    },
    star: {
        type: Number,
        required: [true, 'Star rating is required'],
        min: [1, 'Star rating must be at least 1'],
        max: [5, 'Star rating cannot exceed 5'],
        validate: {
            validator: Number.isInteger,
            message: 'Star rating must be a whole number'
        }
    },
    image: {
        type: String,
        default: null
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isFeatured: {
        type: Boolean,
        default: false
    },

    sortOrder: {
        type: Number,
        default: 0
    },
    eventType: {
        type: String,
        enum: ['wedding', 'corporate', 'birthday', 'anniversary', 'other'],
        default: 'other'
    }
}, {
    timestamps: true
});

// Index for better performance
reviewSchema.index({ star: 1, isActive: 1 });
reviewSchema.index({ relationship: 1, isActive: 1 });
reviewSchema.index({ isFeatured: 1, isActive: 1 });

reviewSchema.index({ sortOrder: 1 });
reviewSchema.index({ eventType: 1 });



// Virtual for average rating calculation
reviewSchema.virtual('ratingText').get(function() {
    const ratings = {
        1: 'Poor',
        2: 'Fair', 
        3: 'Good',
        4: 'Very Good',
        5: 'Excellent'
    };
    return ratings[this.star] || 'Unknown';
});

// Method to get public data
reviewSchema.methods.getPublicData = function() {
    return {
        _id: this._id,
        name: this.name,
        relationship: this.relationship,
        review: this.review,
        star: this.star,
        ratingText: this.ratingText,
        image: this.image,
        isFeatured: this.isFeatured,

        sortOrder: this.sortOrder,
        eventType: this.eventType,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt
    };
};



// Static method to get average rating
reviewSchema.statics.getAverageRating = async function() {
    const result = await this.aggregate([
        { $match: { isActive: true } },
        {
            $group: {
                _id: null,
                averageRating: { $avg: '$star' },
                totalReviews: { $sum: 1 },
                ratingDistribution: {
                    $push: '$star'
                }
            }
        }
    ]);

    if (result.length === 0) {
        return {
            averageRating: 0,
            totalReviews: 0,
            ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        };
    }

    const data = result[0];
    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    
    data.ratingDistribution.forEach(rating => {
        distribution[rating] = (distribution[rating] || 0) + 1;
    });

    return {
        averageRating: Math.round(data.averageRating * 10) / 10,
        totalReviews: data.totalReviews,
        ratingDistribution: distribution
    };
};

module.exports = mongoose.model('Review', reviewSchema);
