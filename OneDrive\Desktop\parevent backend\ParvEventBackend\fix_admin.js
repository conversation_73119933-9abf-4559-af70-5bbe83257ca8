const mongoose = require('mongoose');
const Admin = require('./models/admin.model');
require('dotenv').config();

async function fixAdmin() {
    try {
        // Connect to database
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to database');

        // Find admin <NAME_EMAIL>
        const allAdmins = await Admin.find({});
        console.log(`📋 Found ${allAdmins.length} admins`);

        for (let admin of allAdmins) {
            try {
                const decryptedData = admin.getDecryptedData();
                console.log(`👤 Admin: ${decryptedData.email}`);
                
                if (decryptedData.email === '<EMAIL>') {
                    console.log('🎯 Found target admin! Updating...');
                    
                    // Update admin to bypass 2FA
                    admin.isVerified = true;
                    admin.otpVerified = true;
                    admin.isActive = true;
                    
                    await admin.save();
                    console.log('✅ Admin updated successfully!');
                    
                    // Print admin details
                    console.log('📋 Updated admin details:');
                    console.log(`   ID: ${admin._id}`);
                    console.log(`   Email: ${decryptedData.email}`);
                    console.log(`   isVerified: ${admin.isVerified}`);
                    console.log(`   otpVerified: ${admin.otpVerified}`);
                    console.log(`   isActive: ${admin.isActive}`);
                    
                    break;
                }
            } catch (error) {
                console.log(`⚠️ Could not decrypt admin data: ${error.message}`);
            }
        }

        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

fixAdmin();
