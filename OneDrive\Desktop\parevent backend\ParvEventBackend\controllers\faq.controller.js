const faqService = require("../services/faq.service");

// Create FAQ
const createFAQ = async (req, res) => {
  try {
    const faqData = req.body;
    const result = await faqService.createFAQ(faqData);

    res.status(201).json({
      status: true,
      code: 201,
      message: "FAQ created successfully",
      data: result
    });
  } catch (error) {
    console.error("Error creating FAQ:", error);
    res.status(400).json({
      status: false,
      code: 400,
      message: error.message || "Failed to create FAQ",
      data: null
    });
  }
};

// Get all FAQs
const getAllFAQs = async (req, res) => {
  try {
    const filters = {
      page: req.query.page,
      limit: req.query.limit,
      category: req.query.category,
      status: req.query.status,
      search: req.query.search,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await faqService.getAllFAQs(filters);

    res.status(200).json({
      status: true,
      code: 200,
      message: "FAQs retrieved successfully",
      data: result.faqs,
      pagination: result.pagination
    });
  } catch (error) {
    console.error("Error fetching FAQs:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch FAQs",
      data: null
    });
  }
};

// Get FAQ by ID
const getFAQById = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await faqService.getFAQById(id);

    res.status(200).json({
      status: true,
      code: 200,
      message: "FAQ retrieved successfully",
      data: result
    });
  } catch (error) {
    console.error("Error fetching FAQ:", error);
    const statusCode = error.message === "FAQ not found" ? 404 : 500;
    res.status(statusCode).json({
      status: false,
      code: statusCode,
      message: error.message || "Failed to fetch FAQ",
      data: null
    });
  }
};

// Update FAQ
const updateFAQ = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await faqService.updateFAQ(id, updateData);

    res.status(200).json({
      status: true,
      code: 200,
      message: "FAQ updated successfully",
      data: result
    });
  } catch (error) {
    console.error("Error updating FAQ:", error);
    const statusCode = error.message === "FAQ not found" ? 404 : 400;
    res.status(statusCode).json({
      status: false,
      code: statusCode,
      message: error.message || "Failed to update FAQ",
      data: null
    });
  }
};

// Delete FAQ
const deleteFAQ = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await faqService.deleteFAQ(id);

    res.status(200).json({
      status: true,
      code: 200,
      message: result.message,
      data: null
    });
  } catch (error) {
    console.error("Error deleting FAQ:", error);
    const statusCode = error.message === "FAQ not found" ? 404 : 500;
    res.status(statusCode).json({
      status: false,
      code: statusCode,
      message: error.message || "Failed to delete FAQ",
      data: null
    });
  }
};

// Get FAQs by category
const getFAQsByCategory = async (req, res) => {
  try {
    const { category } = req.params;
    const filters = {
      status: req.query.status,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await faqService.getFAQsByCategory(category, filters);

    res.status(200).json({
      status: true,
      code: 200,
      message: "FAQs retrieved successfully",
      data: result
    });
  } catch (error) {
    console.error("Error fetching FAQs by category:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch FAQs by category",
      data: null
    });
  }
};

// Get FAQ statistics
const getFAQStatistics = async (req, res) => {
  try {
    const result = await faqService.getFAQStatistics();

    res.status(200).json({
      status: true,
      code: 200,
      message: "FAQ statistics retrieved successfully",
      data: result
    });
  } catch (error) {
    console.error("Error fetching FAQ statistics:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch FAQ statistics",
      data: null
    });
  }
};

module.exports = {
  createFAQ,
  getAllFAQs,
  getFAQById,
  updateFAQ,
  deleteFAQ,
  getFAQsByCategory,
  getFAQStatistics
};
