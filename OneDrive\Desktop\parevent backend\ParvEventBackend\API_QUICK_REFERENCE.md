# 🚀 ParvEvent API - Quick Reference Guide

## 📋 Base URL: `http://localhost:8005/api`

---

## 🔐 Authentication
```
POST /auth/signup          - Admin registration
POST /auth/login           - Admin login  
POST /auth/verify-email    - Email verification
POST /auth/verify-2fa      - 2FA verification
GET  /auth/profile         - Get profile (Auth)
POST /auth/logout          - Logout (Auth)
```

---

## 🖼️ Gallery
```
GET    /gallery            - Get all galleries
GET    /gallery/:id        - Get gallery by ID
POST   /gallery            - Create gallery (Auth + File)
PUT    /gallery/:id        - Update gallery (Auth + File)
DELETE /gallery/:id        - Delete gallery (Auth)
```

---

## ⭐ Reviews
```
GET    /reviews            - Get all reviews
GET    /reviews/stats      - Get review statistics
GET    /reviews/:id        - Get review by ID
POST   /reviews            - Create review (Auth + File)
PUT    /reviews/:id        - Update review (Auth + File)
DELETE /reviews/:id        - Delete review (Auth)
```

---

## 🎯 Hero Section
```
GET    /hero-section           - Get all hero sections
GET    /hero-section/primary   - Get primary hero
GET    /hero-section/:id       - Get hero by ID
POST   /hero-section           - Create hero (Auth + File)
PUT    /hero-section/:id       - Update hero (Auth + File)
DELETE /hero-section/:id       - Delete hero (Auth)
```

---

## 🏢 Venues
```
GET    /venues             - Get all venues
GET    /venues/:id         - Get venue by ID
POST   /venues             - Create venue (Auth + Files)
PUT    /venues/:id         - Update venue (Auth + Files)
DELETE /venues/:id         - Delete venue (Auth)
```

---

## 📝 Venue Bookings
```
POST   /venue-bookings             - Submit booking (Public)
GET    /venue-bookings             - Get all bookings (Auth)
GET    /venue-bookings/upcoming    - Get upcoming events (Auth)
GET    /venue-bookings/:id         - Get booking by ID (Auth)
PUT    /venue-bookings/:id         - Update booking (Auth)
DELETE /venue-bookings/:id         - Delete booking (Auth)
```

---

## 🛠️ Services
```
GET    /services                    - Get all services
GET    /services/featured           - Get featured services
GET    /services/statistics         - Get service statistics
GET    /services/category/:category - Get services by category
GET    /services/:id                - Get service by ID
POST   /services                    - Create service (Auth + Files)
PUT    /services/:id                - Update service (Auth + Files)
DELETE /services/:id                - Delete service (Auth)
```

---

## 👥 Team
```
GET    /team               - Get all team members
GET    /team/:id           - Get team member by ID
POST   /team               - Create team member (Auth + File)
PUT    /team/:id           - Update team member (Auth + File)
DELETE /team/:id           - Delete team member (Auth)
```

---

## 📝 Blogs
```
GET    /blogs                      - Get all blogs
GET    /blogs/category/:category   - Get blogs by category
GET    /blogs/:id                  - Get blog by ID
POST   /blogs                      - Create blog (Auth + File)
PUT    /blogs/:id                  - Update blog (Auth + File)
DELETE /blogs/:id                  - Delete blog (Auth)
```

---

## 💬 Comments
```
POST   /comments                    - Create comment (Public)
GET    /comments/approved           - Get approved comments
GET    /comments/blog/:blogId       - Get comments by blog
GET    /comments/blog/:blogId/count - Get comment count
GET    /comments                    - Get all comments (Auth)
GET    /comments/statistics         - Get comment stats (Auth)
GET    /comments/:id                - Get comment by ID (Auth)
PUT    /comments/:id                - Update comment (Auth)
DELETE /comments/:id                - Delete comment (Auth)
```

---

## ❓ FAQs
```
GET    /faqs                       - Get all FAQs
GET    /faqs/statistics            - Get FAQ statistics
GET    /faqs/category/:category    - Get FAQs by category
GET    /faqs/:id                   - Get FAQ by ID
POST   /faqs                       - Create FAQ (Auth)
PUT    /faqs/:id                   - Update FAQ (Auth)
DELETE /faqs/:id                   - Delete FAQ (Auth)
```

---

## 📞 Contacts
```
POST   /contacts                   - Create contact (Public)
GET    /contacts                   - Get all contacts (Auth)
GET    /contacts/statistics        - Get contact stats (Auth)
GET    /contacts/status/:status    - Get contacts by status (Auth)
GET    /contacts/service/:service  - Get contacts by service (Auth)
GET    /contacts/:id               - Get contact by ID (Auth)
PUT    /contacts/:id               - Update contact (Auth)
DELETE /contacts/:id               - Delete contact (Auth)
```

---

## 📋 Contact Form
```
POST   /contact-form/submit        - Submit contact form (Public)
```

---

## 📁 File Upload
```
POST   /upload/single              - Upload single file
POST   /upload/multiple            - Upload multiple files
```

---

## 🔧 Utilities
```
GET    /test-cors                  - Test CORS configuration
GET    /health                     - Health check
```

---

## 🎯 Fixed ObjectIDs for Frontend Routing

```javascript
// Gallery
gallery_1703123456789_abc123def

// Reviews  
review_1703123456789_xyz789ghi

// Hero Section
hero_1703123456789_mno456pqr

// Venues
venue_1703123456789_stu123vwx

// Services
service_1703123456789_def456ghi

// Team
team_1703123456789_jkl789mno

// Blogs
blog_1703123456789_pqr123stu
```

---

## 📁 S3 Folder Structure

```
parvevent-bucket/
├── gallery/          # Gallery images
├── reviews/          # Review images  
├── hero-section/     # Hero section images
├── venues/           # Venue images
├── services/         # Service images and icons
├── team/             # Team member images
├── blogs/            # Blog images
└── uploads/          # General file uploads
```

---

## 🔐 Authentication Header

```javascript
headers: {
  'Authorization': 'Bearer <your-jwt-token>'
}
```

---

## 📊 Response Format

```javascript
// Success
{
  "success": true,
  "message": "Operation successful",
  "data": {...}
}

// Error
{
  "success": false,
  "message": "Error description",
  "errors": ["Details"]
}

// Pagination
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "itemsPerPage": 10
    }
  }
}
```

---

## 🚀 Frontend Integration

```javascript
// Fetch data
const gallery = await fetch('http://localhost:8005/api/gallery');
const data = await gallery.json();

// Navigate with fixed IDs
router.push(`/gallery/${item.fixedId}`);

// Submit with auth
const response = await fetch('http://localhost:8005/api/gallery', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'multipart/form-data'
  },
  body: formData
});
```

---

## ✅ Total APIs: 88+ Endpoints

**All APIs tested and ready for production! 🎉**
