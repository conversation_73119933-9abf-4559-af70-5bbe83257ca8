const { S3Client } = require('@aws-sdk/client-s3');
const multer = require('multer');
const multerS3 = require('multer-s3');
require('dotenv').config();

// Configure AWS S3 Client
const s3Client = new S3Client({
    region: process.env.S3_REGION,
    credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY,
        secretAccessKey: process.env.S3_SECRET_KEY,
    },
});

// Test S3 Connection
const testS3Connection = async () => {
    try {
        const { ListBucketsCommand } = require('@aws-sdk/client-s3');
        const command = new ListBucketsCommand({});
        const response = await s3Client.send(command);

        console.log('✅ AWS S3 Connection Successful!');
        // console.log('📦 Available Buckets:', response.Buckets.map(bucket => bucket.Name));

        // Check if our specific bucket exists
        const bucketExists = response.Buckets.some(bucket => bucket.Name === process.env.S3_BUCKET_NAME);
        if (bucketExists) {
            console.log(`✅ Target bucket '${process.env.S3_BUCKET_NAME}' found!`);
        } else {
            console.log(`⚠️  Target bucket '${process.env.S3_BUCKET_NAME}' not found in the list.`);
        }

        return true;
    } catch (error) {
        console.error('❌ AWS S3 Connection Failed:', error.message);
        return false;
    }
};

// Configure Multer with S3
const upload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `uploads/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: function (req, file, cb) {
        // Allow images and documents
        const allowedMimes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];

        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only images and documents are allowed.'), false);
        }
    }
});

// Blog-specific upload configuration
const blogUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            cb(null, { fieldName: file.fieldname, uploadType: 'blog' });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp for blog folder
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `blog/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit for blog images
    },
    fileFilter: function (req, file, cb) {
        // Allow only images for blog posts
        const allowedMimes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];

        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) are allowed for blog posts.'), false);
        }
    }
});

// Team-specific upload configuration
const teamUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            cb(null, { fieldName: file.fieldname, uploadType: 'team' });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp for team folder
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `team/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit for team images
    },
    fileFilter: function (req, file, cb) {
        // Allow only images for team members
        const allowedMimes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];

        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) are allowed for team photos.'), false);
        }
    }
});

// Service-specific upload configuration for SVG icons
const serviceIconUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            cb(null, { fieldName: file.fieldname, uploadType: 'service-icon' });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp for services/icons folder
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `services/icons/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 2 * 1024 * 1024, // 2MB limit for SVG icons
    },
    fileFilter: function (req, file, cb) {
        // Allow only SVG files for service icons
        const allowedMimes = [
            'image/svg+xml',
            'image/svg'
        ];

        if (allowedMimes.includes(file.mimetype) || file.originalname.toLowerCase().endsWith('.svg')) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only SVG files are allowed for service icons.'), false);
        }
    }
});

// Service-specific upload configuration for videos
const serviceVideoUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            cb(null, { fieldName: file.fieldname, uploadType: 'service-video' });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp for services/videos folder
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `services/videos/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 100 * 1024 * 1024, // 100MB limit for videos (approximately 5 minutes of video)
    },
    fileFilter: function (req, file, cb) {
        // Allow video files for services
        const allowedMimes = [
            'video/mp4',
            'video/webm',
            'video/quicktime',
            'video/x-msvideo',
            'video/avi',
            'video/x-ms-wmv',
            'video/x-matroska',
            'video/mkv'
        ];

        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only video files (MP4, WebM, MOV, AVI, WMV, MKV) are allowed.'), false);
        }
    }
});

// Service-specific upload configuration for multiple files (icon + video)
const serviceMultipleUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            const uploadType = file.fieldname === 'icons' ? 'service-icon' : 'service-image';
            cb(null, { fieldName: file.fieldname, uploadType: uploadType });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();

            // Determine folder based on field name
            const folder = file.fieldname === 'icons' ? 'services/icons' : 'services/images';
            const fileName = `${folder}/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 100 * 1024 * 1024, // 100MB limit (will be checked per file type in fileFilter)
    },
    fileFilter: function (req, file, cb) {
        if (file.fieldname === 'icons') {
            // SVG files for icons
            const allowedIconMimes = ['image/svg+xml', 'image/svg'];
            if (allowedIconMimes.includes(file.mimetype) || file.originalname.toLowerCase().endsWith('.svg')) {
                // Check file size for icons (2MB max)
                if (file.size && file.size > 2 * 1024 * 1024) {
                    cb(new Error('Icon file size too large. Maximum 2MB allowed.'), false);
                } else {
                    cb(null, true);
                }
            } else {
                cb(new Error('Invalid icon file type. Only SVG files are allowed.'), false);
            }
        } else if (file.fieldname === 'image') {
            // Image files
            const allowedImageMimes = [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'
            ];
            if (allowedImageMimes.includes(file.mimetype)) {
                cb(null, true);
            } else {
                cb(new Error('Invalid image file type. Only image files (JPEG, PNG, GIF, WebP) are allowed.'), false);
            }
        } else {
            cb(new Error('Invalid field name. Only "icons" and "image" fields are allowed.'), false);
        }
    }
});

// Project-specific upload configuration for multiple images (big image + 3 mini images)
const projectMultipleUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            const uploadType = file.fieldname === 'bigImage' ? 'project-big-image' : 'project-mini-image';
            cb(null, { fieldName: file.fieldname, uploadType: uploadType });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();

            // All project images go to projects folder
            const fileName = `projects/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit for project images
    },
    fileFilter: function (req, file, cb) {
        // Allow only images for projects
        const allowedMimes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];

        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) are allowed for project images.'), false);
        }
    }
});

// Gallery-specific upload configuration
const galleryUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            cb(null, { fieldName: file.fieldname, uploadType: 'gallery' });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp for gallery folder
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `gallery/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit for gallery images
    },
    fileFilter: function (req, file, cb) {
        // Check file type
        const allowedMimes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];

        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) are allowed for gallery.'), false);
        }
    }
});

// Review-specific upload configuration
const reviewUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            cb(null, { fieldName: file.fieldname, uploadType: 'review' });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp for reviews folder
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `reviews/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit for review images
    },
    fileFilter: function (req, file, cb) {
        // Check file type
        const allowedMimes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];

        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) are allowed for reviews.'), false);
        }
    }
});

// Hero section upload configuration
const heroUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            cb(null, { fieldName: file.fieldname, uploadType: 'hero-section' });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp for hero section folder
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `hero-section/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 15 * 1024 * 1024, // 15MB limit for hero section images
    },
    fileFilter: function (req, file, cb) {
        // Check file type
        const allowedMimes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];

        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) are allowed for hero section.'), false);
        }
    }
});

// Venue upload configuration
const venueUpload = multer({
    storage: multerS3({
        s3: s3Client,
        bucket: process.env.S3_BUCKET_NAME,
        metadata: function (req, file, cb) {
            cb(null, { fieldName: file.fieldname, uploadType: 'venue' });
        },
        key: function (req, file, cb) {
            // Generate unique filename with timestamp for venues folder
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `venues/${uniqueSuffix}.${fileExtension}`;
            cb(null, fileName);
        },
        contentType: multerS3.AUTO_CONTENT_TYPE,
    }),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit for venue images
    },
    fileFilter: function (req, file, cb) {
        // Check file type
        const allowedMimes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];

        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) are allowed for venues.'), false);
        }
    }
});

// Export the configured upload middleware and S3 client
module.exports = {
    s3Client,
    upload,
    testS3Connection,
    // Single file upload
    uploadSingle: (fieldName) => upload.single(fieldName),
    // Multiple files upload
    uploadMultiple: (fieldName, maxCount = 5) => upload.array(fieldName, maxCount),
    // Multiple fields upload
    uploadFields: (fields) => upload.fields(fields),
    // Blog-specific uploads
    blogUploadSingle: (fieldName) => blogUpload.single(fieldName),
    blogUploadMultiple: (fieldName, maxCount = 3) => blogUpload.array(fieldName, maxCount),
    // Team-specific uploads
    teamUploadSingle: (fieldName) => teamUpload.single(fieldName),
    teamUploadMultiple: (fieldName, maxCount = 3) => teamUpload.array(fieldName, maxCount),
    // Service-specific uploads
    serviceIconUploadSingle: (fieldName) => serviceIconUpload.single(fieldName),
    serviceVideoUploadSingle: (fieldName) => serviceVideoUpload.single(fieldName),
    // serviceUploadFields: (fields) => serviceMultipleUpload.fields(fields), // Removed - using specific config below
    // Service multiple upload for icon and video together (OLD - REMOVED)
    // serviceUploadMultiple: serviceMultipleUpload.fields([
    //     { name: 'icon', maxCount: 1 },
    //     { name: 'video', maxCount: 1 }
    // ]),
    // Project-specific uploads
    projectUploadMultiple: projectMultipleUpload.fields([
        { name: 'bigImage', maxCount: 1 },
        { name: 'miniImages', maxCount: 3 }
    ]),
    // Career form CV upload
    careerUploadSingle: (fieldName) => upload.single(fieldName),
    // Gallery-specific uploads
    galleryUploadSingle: (fieldName) => galleryUpload.single(fieldName),
    galleryUploadMultiple: (fieldName, maxCount = 5) => galleryUpload.array(fieldName, maxCount),
    // Review-specific uploads
    reviewUploadSingle: (fieldName) => reviewUpload.single(fieldName),
    // Hero section uploads
    heroUploadSingle: (fieldName) => heroUpload.single(fieldName),
    // Venue uploads
    venueUploadSingle: (fieldName) => venueUpload.single(fieldName),
    venueUploadMultiple: venueUpload.fields([
        { name: 'image', maxCount: 1 },
        { name: 'images', maxCount: 10 }
    ]),
    // Service uploads (NEW - separate configuration)
    serviceUploadFields: multer({
        storage: multerS3({
            s3: s3Client,
            bucket: process.env.S3_BUCKET_NAME,
            metadata: function (req, file, cb) {
                const uploadType = file.fieldname === 'icons' ? 'service-icon-image' : 'service-image';
                cb(null, { fieldName: file.fieldname, uploadType: uploadType });
            },
            key: function (req, file, cb) {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
                const fileExtension = file.originalname.split('.').pop();
                const folder = file.fieldname === 'icons' ? 'services/icons' : 'services/images';
                const fileName = `${folder}/${uniqueSuffix}.${fileExtension}`;
                cb(null, fileName);
            }
        }),
        limits: {
            fileSize: 10 * 1024 * 1024, // 10MB limit
        },
        fileFilter: function (req, file, cb) {
            if (file.fieldname === 'icons') {
                // Image files for icons (PNG, JPG preferred)
                const allowedIconMimes = [
                    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'
                ];
                if (allowedIconMimes.includes(file.mimetype)) {
                    cb(null, true);
                } else {
                    cb(new Error('Invalid icon file type. Only image files (JPEG, PNG, GIF, WebP) are allowed for icons.'), false);
                }
            } else if (file.fieldname === 'image') {
                // Service image files
                const allowedImageMimes = [
                    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'
                ];
                if (allowedImageMimes.includes(file.mimetype)) {
                    cb(null, true);
                } else {
                    cb(new Error('Invalid image file type. Only image files (JPEG, PNG, GIF, WebP) are allowed.'), false);
                }
            } else {
                cb(new Error('Invalid field name. Only "icons" and "image" fields are allowed for services.'), false);
            }
        }
    }).fields([
        { name: 'icons', maxCount: 1 },
        { name: 'image', maxCount: 1 }
    ]),
    // Team uploads (updated for simplified team model)
    teamUploadSingle: (fieldName) => upload.single(fieldName)
};