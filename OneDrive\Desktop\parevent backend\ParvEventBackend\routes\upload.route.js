const express = require('express');
const { uploadSingle, uploadMultiple } = require('../config/awsclouds3');
const router = express.Router();

// Single file upload endpoint
router.post('/single', uploadSingle('file'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'No file uploaded'
            });
        }

        res.status(200).json({
            success: true,
            message: 'File uploaded successfully',
            data: {
                filename: req.file.key,
                location: req.file.location,
                bucket: req.file.bucket,
                size: req.file.size,
                mimetype: req.file.mimetype
            }
        });
    } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({
            success: false,
            message: 'File upload failed',
            error: error.message
        });
    }
});

// Multiple files upload endpoint
router.post('/multiple', uploadMultiple('files', 5), (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No files uploaded'
            });
        }

        const uploadedFiles = req.files.map(file => ({
            filename: file.key,
            location: file.location,
            bucket: file.bucket,
            size: file.size,
            mimetype: file.mimetype
        }));

        res.status(200).json({
            success: true,
            message: `${req.files.length} files uploaded successfully`,
            data: uploadedFiles
        });
    } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({
            success: false,
            message: 'Files upload failed',
            error: error.message
        });
    }
});

// Test endpoint to check if upload service is working
router.get('/test', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Upload service is working',
        endpoints: {
            single: 'POST /api/upload/single - Upload single file (field name: file)',
            multiple: 'POST /api/upload/multiple - Upload multiple files (field name: files, max: 5)'
        }
    });
});

module.exports = router;
