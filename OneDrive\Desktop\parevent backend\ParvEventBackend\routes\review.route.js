const express = require('express');
const router = express.Router();
const {
    createReview,
    getAllReviews,
    getReviewById,
    updateReview,
    deleteReview,
    getReviewStats
} = require('../controllers/review.controller');
const { reviewUploadSingle } = require('../config/awsclouds3');
const authMiddleware = require('../middleware/auth.middleware');

// Apply security headers to all review routes
router.use(authMiddleware.securityHeaders);

// Public routes (no authentication required)
router.get('/', getAllReviews);
router.get('/stats', getReviewStats);
router.get('/:id', getReviewById);

// Protected routes (authentication required)
router.post('/', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    reviewUploadSingle('image'),
    createReview
);

router.put('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    reviewUploadSingle('image'),
    updateReview
);

router.delete('/:id', 
    authMiddleware.authenticateAdmin,
    authMiddleware.requireAdmin,
    deleteReview
);

module.exports = router;
