const mongoose = require('mongoose');

const venueFormSchema = new mongoose.Schema({
    fullName: {
        type: String,
        required: [true, 'Full name is required'],
        trim: true,
        maxlength: [100, 'Full name cannot exceed 100 characters']
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        trim: true,
        lowercase: true,
        validate: {
            validator: function(v) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
            },
            message: 'Please enter a valid email address'
        }
    },
    phoneNumber: {
        type: String,
        required: [true, 'Phone number is required'],
        trim: true,
        validate: {
            validator: function(v) {
                return /^[6-9]\d{9}$/.test(v);
            },
            message: 'Please enter a valid 10-digit phone number'
        }
    },
    venueId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Venue',
        required: [true, 'Venue selection is required']
    },
    venueName: {
        type: String,
        required: [true, 'Venue name is required'],
        trim: true,
        maxlength: [200, 'Venue name cannot exceed 200 characters']
    },
    dateOfPlan: {
        type: Date,
        required: [true, 'Date of plan is required'],
        validate: {
            validator: function(date) {
                return date > new Date();
            },
            message: 'Date of plan must be in the future'
        }
    },
    message: {
        type: String,
        required: [true, 'Message is required'],
        trim: true,
        maxlength: [1000, 'Message cannot exceed 1000 characters']
    },
    status: {
        type: String,
        enum: ['new', 'in-progress', 'resolved', 'closed'],
        default: 'new'
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium'
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

// Index for better performance
venueFormSchema.index({ status: 1, isActive: 1 });
venueFormSchema.index({ dateOfPlan: 1 });
venueFormSchema.index({ venueId: 1 });
venueFormSchema.index({ email: 1 });
venueFormSchema.index({ phoneNumber: 1 });
venueFormSchema.index({ priority: 1, status: 1 });
venueFormSchema.index({ createdAt: -1 });

// Method to get public data
venueFormSchema.methods.getPublicData = function() {
    return {
        _id: this._id,
        fullName: this.fullName,
        email: this.email,
        phoneNumber: this.phoneNumber,
        venueId: this.venueId,
        venueName: this.venueName,
        dateOfPlan: this.dateOfPlan,
        message: this.message,
        status: this.status,
        priority: this.priority,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt
    };
};

// Method to get admin data (includes sensitive info)
venueFormSchema.methods.getAdminData = function() {
    return this.getPublicData();
};

// Static method to get bookings by status
venueFormSchema.statics.getByStatus = function(status) {
    return this.find({ status, isActive: true }).sort({ createdAt: -1 });
};

// Static method to get upcoming events
venueFormSchema.statics.getUpcomingEvents = function(days = 30) {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + days);

    return this.find({
        dateOfPlan: { $gte: startDate, $lte: endDate },
        status: { $in: ['resolved', 'new'] },
        isActive: true
    }).sort({ dateOfPlan: 1 });
};

module.exports = mongoose.model('VenueForm', venueFormSchema);
