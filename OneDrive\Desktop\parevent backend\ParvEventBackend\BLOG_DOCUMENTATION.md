# 📝 Blog Management System - Complete Documentation

## Overview
The Blog Management System provides comprehensive functionality for creating, managing, and displaying blog posts with advanced features including image uploads, categorization, keyword tagging, search capabilities, and comment management.

## 🏗️ Architecture

### Core Components
- **Model**: `models/blog.model.js` - Blog data structure
- **Service**: `services/blog.service.js` - Business logic
- **Controller**: `controllers/blog.controller.js` - Request handling
- **Routes**: `routes/blog.route.js` - API endpoints
- **Comments**: Complete comment system with moderation

### Related Components
- **Comment Model**: `models/postComment.model.js`
- **Comment Service**: `services/postComment.service.js`
- **Comment Controller**: `controllers/postComment.controller.js`
- **Comment Routes**: `routes/postComment.route.js`

## 📊 Database Schema

### Blog Model
```javascript
{
  id: Number (auto-increment, unique),
  title: String (required, max 200 chars),
  description: String (required, max 10000 chars),
  imageUrl: String (optional, S3 URL),
  category: [String] (required, array, lowercase),
  keywords: [String] (optional, array, lowercase),
  views: Number (default: 0),
  createdAt: Date,
  updatedAt: Date
}
```

### Comment Model
```javascript
{
  id: Number (auto-increment, unique),
  blogId: Mixed (ObjectId or Number),
  name: String (required, max 100 chars),
  email: String (required, validated),
  comment: String (required, max 1000 chars),
  status: String (enum: pending, approved, rejected, spam),
  isVisible: Boolean (default: false),
  moderatedBy: String (admin email),
  moderatedAt: Date,
  ipAddress: String,
  userAgent: String,
  createdAt: Date,
  updatedAt: Date
}
```

## 🔗 API Endpoints

### Blog Management

#### 1. Create Blog Post
```http
POST /blogs
Content-Type: multipart/form-data
```

**Request Body:**
```javascript
{
  "title": "Blog Title",
  "description": "Blog content with formatting",
  "category": "technology,web-development", // String or Array
  "keywords": "react,nodejs,javascript", // String or Array
  "image": File // Optional image file
}
```

**Response:**
```javascript
{
  "status": true,
  "code": 201,
  "message": "Blog created successfully",
  "data": {
    "_id": "ObjectId",
    "id": 1,
    "title": "Blog Title",
    "description": "Blog content",
    "imageUrl": "https://s3-url/blog/image.jpg",
    "category": ["technology", "web-development"],
    "keywords": ["react", "nodejs", "javascript"],
    "views": 0,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 2. Get All Blogs
```http
GET /blogs?page=1&pageSize=10&sortBy=createdAt&sortOrder=desc&category=technology&search=react&keywords=javascript
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 40)
- `sortBy`: Sort field (default: createdAt)
- `sortOrder`: asc/desc (default: desc)
- `category`: Filter by category
- `search`: Search in title, description, keywords, category
- `keywords`: Filter by keywords

**Response:**
```javascript
{
  "status": true,
  "code": 200,
  "message": "Blogs retrieved successfully",
  "data": {
    "data": [/* blog objects */],
    "totalCount": 50,
    "totalPages": 5,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "currentPage": 1,
    "pageSize": 10,
    "filters": {
      "category": "technology",
      "search": "react",
      "keywords": "javascript"
    }
  }
}
```

#### 3. Get Blog by ID
```http
GET /blogs/:id
```

**Response:**
```javascript
{
  "status": true,
  "code": 200,
  "message": "Blog retrieved successfully",
  "data": {
    // Blog object with incremented views
  }
}
```

#### 4. Update Blog
```http
PUT /blogs/:id
Content-Type: multipart/form-data
```

**Request Body:** Same as create, all fields optional

#### 5. Delete Blog
```http
DELETE /blogs/:id
```

#### 6. Get Blogs by Category
```http
GET /blogs/category/:category?page=1&pageSize=10&sortBy=createdAt&sortOrder=desc
```

### Comment Management

#### 1. Create Comment (Public)
```http
POST /comments
Content-Type: application/json
```

**Request Body:**
```javascript
{
  "blogId": "ObjectId or Number",
  "name": "Commenter Name",
  "email": "<EMAIL>",
  "comment": "Comment text"
}
```

#### 2. Get Comments by Blog ID (Public)
```http
GET /comments/blog/:blogId?status=approved&page=1&limit=20
```

#### 3. Get Comment Count by Blog ID (Public)
```http
GET /comments/blog/:blogId/count
```

#### 4. Get All Comments (Admin)
```http
GET /comments?page=1&limit=10&status=pending&search=keyword
```

#### 5. Update Comment (Admin - Moderation)
```http
PUT /comments/:id
Content-Type: application/json
```

**Request Body:**
```javascript
{
  "status": "approved", // approved, rejected, spam
  "isVisible": true
}
```

#### 6. Delete Comment (Admin)
```http
DELETE /comments/:id
```

#### 7. Get Comment Statistics (Admin)
```http
GET /comments/statistics
```

## 🖼️ Image Upload Configuration

### S3 Configuration
- **Folder**: `blog/`
- **File Size Limit**: 5MB
- **Allowed Formats**: JPEG, PNG, GIF, WebP
- **Naming**: `blog/{timestamp}-{random}.{extension}`

### Upload Process
1. File validation (type, size)
2. Upload to S3 blog folder
3. Store S3 URL in database
4. Return blog object with imageUrl

## 🔍 Search & Filtering Features

### Search Functionality
- **Title Search**: Case-insensitive regex
- **Description Search**: Full-text search
- **Category Search**: Array-based matching
- **Keyword Search**: Array-based matching
- **Combined Search**: OR operation across all fields

### Filtering Options
- **Category Filter**: Multiple categories supported
- **Keyword Filter**: Multiple keywords supported
- **Date Range**: Via sortBy and sortOrder
- **Pagination**: Page-based with metadata

## 📈 Analytics & Tracking

### View Tracking
- Automatic view increment on blog access
- View count stored in database
- No duplicate view prevention (each request counts)

### Comment Analytics
- Total comments per blog
- Comment status distribution
- Moderation statistics
- User engagement metrics

## 🛡️ Security Features

### Comment Moderation
- **Default Status**: All comments start as "pending"
- **Admin Approval**: Required for visibility
- **Status Options**: pending, approved, rejected, spam
- **Tracking**: IP address and user agent logging
- **Moderation Log**: Who approved/rejected and when

### Input Validation
- **Title**: Required, max 200 characters
- **Description**: Required, max 10000 characters
- **Email**: Valid email format required
- **Comment**: Required, max 1000 characters

### File Upload Security
- File type validation
- File size limits
- Unique filename generation
- S3 secure storage

## 🔄 Legacy Support

### Backward Compatibility Routes
```http
POST /createblog          → POST /blogs
GET /getallblogs         → GET /blogs
GET /getblog/:id         → GET /blogs/:id
PUT /updateblog/:id      → PUT /blogs/:id
DELETE /deleteblog/:id   → DELETE /blogs/:id
```

## 📝 Usage Examples

### Creating a Blog Post with Image
```javascript
const formData = new FormData();
formData.append('title', 'My Blog Post');
formData.append('description', 'This is the content...');
formData.append('category', 'technology,tutorial');
formData.append('keywords', 'javascript,react,tutorial');
formData.append('image', imageFile);

fetch('/blogs', {
  method: 'POST',
  body: formData
});
```

### Searching Blogs
```javascript
// Search for React tutorials in technology category
fetch('/blogs?search=react&category=technology&keywords=tutorial&page=1&pageSize=10');
```

### Moderating Comments
```javascript
// Approve a comment
fetch('/comments/123', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    status: 'approved',
    isVisible: true
  })
});
```

## 🚀 Best Practices

### Content Management
1. Use descriptive titles (under 200 characters)
2. Format descriptions with proper HTML/Markdown
3. Add relevant categories and keywords
4. Upload high-quality images (under 5MB)

### Comment Moderation
1. Review comments regularly
2. Set clear moderation guidelines
3. Respond to approved comments
4. Monitor for spam patterns

### Performance Optimization
1. Use pagination for large result sets
2. Implement caching for popular blogs
3. Optimize images before upload
4. Use appropriate sorting and filtering

## 🔧 Configuration

### Environment Variables
```env
S3_BUCKET_NAME=your-bucket-name
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=your-region
```

### Database Indexes
- Text search index on comments
- Category and keyword indexes
- Date-based indexes for sorting
- Compound indexes for common queries

## 📋 Error Handling

### Common Error Responses
```javascript
// Validation Error
{
  "status": false,
  "code": 400,
  "message": "Title, description, and category are required",
  "data": null
}

// Not Found Error
{
  "status": false,
  "code": 404,
  "message": "Blog not found",
  "data": null
}

// Server Error
{
  "status": false,
  "code": 500,
  "message": "Failed to create blog",
  "data": null
}
```

This documentation covers the complete blog management system with all features, endpoints, and implementation details.
