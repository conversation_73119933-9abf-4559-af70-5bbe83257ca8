const express = require("express");
const {
  createContact,
  getAllContacts,
  getContactById,
  updateContact,
  deleteContact,
  getContactStatistics,
  getContactsByStatus,
  getContactsByService
} = require("../controllers/contact.controller");

const router = express.Router();

// Contact CRUD routes

// POST - Create new contact
router.post("/contacts", createContact);

// GET - Get all contacts with filtering and pagination
router.get("/contacts", getAllContacts);

// GET - Get contact statistics
router.get("/contacts/statistics", getContactStatistics);

// GET - Get contacts by status
router.get("/contacts/status/:status", getContactsByStatus);

// GET - Get contacts by service
router.get("/contacts/service/:service", getContactsByService);

// GET - Get contact by ID
router.get("/contacts/:id", getContactById);

// PUT - Update contact by ID
router.put("/contacts/:id", updateContact);

// DELETE - Delete contact by ID
router.delete("/contacts/:id", deleteContact);

module.exports = router;
