const express = require("express");
const {
  createBlogPost,
  getBlogById,
  updateBlog,
  deleteBlogById,
  getAllBlogs,
  getBlogsByCategory
} = require("../controllers/blog.controller");
const { blogUploadSingle } = require("../config/awsclouds3");
const router = express.Router();

// Blog CRUD routes with image upload support
router.post("/blogs", blogUploadSingle('image'), createBlogPost);
router.get("/blogs", getAllBlogs);
router.get("/blogs/category/:category", getBlogsByCategory);
router.get("/blogs/:id", getBlogById);
router.put("/blogs/:id", blogUploadSingle('image'), updateBlog);
router.delete("/blogs/:id", deleteBlogById);

// Legacy routes for backward compatibility
router.post("/createblog", blogUploadSingle('image'), createBlogPost);
router.get("/getallblogs", getAllBlogs);
router.get("/getblog/:id", getBlogById);
router.put("/updateblog/:id", blogUploadSingle('image'), updateBlog);
router.delete("/deleteblog/:id", deleteBlogById);

module.exports = router;
