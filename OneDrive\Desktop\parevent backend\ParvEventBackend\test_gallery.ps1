$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTU1ODgxLCJleHAiOjE3NTExNTk0ODF9.GPbgqGvLiAH_0CeNZnkMcomH47jaTMAssmL4Zmhiokw'
}

Write-Host "Testing gallery creation..."

# Test 1: Gallery with keywords as array
$galleryData1 = @{
    title = "Test Gallery 1"
    description = "Test description"
    keywords = @("wedding", "photography", "beautiful")
    category = "wedding"
} | ConvertTo-Json

Write-Host "Test 1: Keywords as array"
try {
    # Note: This is just testing the validation, actual creation needs image upload
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/gallery' -Method POST -Headers $headers -ContentType 'application/json' -Body $galleryData1
    Write-Host "SUCCESS: Gallery creation with array keywords!"
} catch {
    Write-Host "Expected error (no image): $($_.Exception.Message)"
}

# Test 2: Gallery with keywords as string
$galleryData2 = @{
    title = "Test Gallery 2"
    description = "Test description"
    keywords = "wedding,photography,beautiful"
    category = "wedding"
} | ConvertTo-Json

Write-Host "`nTest 2: Keywords as comma-separated string"
try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/gallery' -Method POST -Headers $headers -ContentType 'application/json' -Body $galleryData2
    Write-Host "SUCCESS: Gallery creation with string keywords!"
} catch {
    Write-Host "Expected error (no image): $($_.Exception.Message)"
}

Write-Host "`nTesting gallery GET endpoint..."
try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/gallery' -Headers $headers
    Write-Host "SUCCESS: Gallery GET endpoint working!"
    Write-Host "Found $($result.data.galleries.Count) galleries"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}
