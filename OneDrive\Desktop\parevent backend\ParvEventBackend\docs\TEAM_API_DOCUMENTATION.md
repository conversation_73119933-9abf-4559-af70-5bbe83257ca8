# 👥 Team API Documentation

## Overview
Complete API documentation for Team management in ParvEvent Backend. Team represents company team members and staff profiles.

## Base URL
```
http://localhost:8005/api/team
```

## Authentication
All endpoints require admin authentication:
```
Authorization: Bearer <JWT_TOKEN>
```

---

## 📋 **API Endpoints**

### 1. Create Team Member
**POST** `/api/team`

Creates a new team member with image upload.

**Content-Type:** `multipart/form-data`

**Form Fields:**
```javascript
{
  "name": "<PERSON>",                           // Required, max 100 chars
  "position": "Event Manager",                 // Optional, max 100 chars
  "department": "Operations",                  // Optional, max 50 chars
  "bio": "Experienced event manager...",       // Optional, max 500 chars
  "email": "<EMAIL>",              // Optional, valid email
  "phone": "9876543210",                      // Optional, 10 digits
  "socialLinks": {                            // Optional
    "linkedin": "https://linkedin.com/in/johndoe",
    "twitter": "https://twitter.com/johndoe",
    "instagram": "https://instagram.com/johndoe",
    "facebook": "https://facebook.com/johndoe"
  },
  "skills": ["Event Planning", "Team Management"], // Optional array
  "experience": "5 years",                    // Optional, max 50 chars
  "education": "MBA in Event Management",     // Optional, max 200 chars
  "achievements": [                           // Optional array
    "Best Event Manager 2023",
    "Successfully managed 100+ events"
  ],
  "isActive": true,                           // Optional, default true
  "sortOrder": 1                              // Optional, default 0
}

// File upload (required):
image: File                    // Team member profile image
```

**Response:**
```javascript
{
  "success": true,
  "message": "Team member created successfully",
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "John Doe",
    "position": "Event Manager",
    "department": "Operations",
    "bio": "Experienced event manager with 5+ years...",
    "email": "<EMAIL>",
    "phone": "9876543210",
    "image": "https://s3.amazonaws.com/bucket/team/john-doe.jpg",
    "socialLinks": {
      "linkedin": "https://linkedin.com/in/johndoe",
      "twitter": "https://twitter.com/johndoe",
      "instagram": "https://instagram.com/johndoe",
      "facebook": "https://facebook.com/johndoe"
    },
    "skills": ["Event Planning", "Team Management"],
    "experience": "5 years",
    "education": "MBA in Event Management",
    "achievements": [
      "Best Event Manager 2023",
      "Successfully managed 100+ events"
    ],
    "isActive": true,
    "sortOrder": 1,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

### 2. Get All Team Members
**GET** `/api/team`

Retrieves all active team members with pagination and filtering.

**Query Parameters:**
```
?page=1                    // Page number (default: 1)
&limit=10                  // Items per page (default: 10, max: 100)
&sortBy=sortOrder          // Sort field (default: sortOrder)
&sortOrder=asc             // Sort order: asc/desc (default: asc)
&department=Operations     // Filter by department
&position=Manager          // Filter by position
&isActive=true             // Filter active members
&search=john               // Search in name, position, department
```

**Response:**
```javascript
{
  "success": true,
  "data": {
    "teamMembers": [
      {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
        "name": "John Doe",
        "position": "Event Manager",
        "department": "Operations",
        "bio": "Experienced event manager...",
        "email": "<EMAIL>",
        "phone": "9876543210",
        "image": "https://s3.amazonaws.com/bucket/team/john-doe.jpg",
        "socialLinks": {
          "linkedin": "https://linkedin.com/in/johndoe",
          "twitter": "https://twitter.com/johndoe"
        },
        "skills": ["Event Planning", "Team Management"],
        "experience": "5 years",
        "isActive": true,
        "sortOrder": 1,
        "createdAt": "2023-12-01T10:00:00.000Z"
      },
      {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b4",
        "name": "Jane Smith",
        "position": "Creative Director",
        "department": "Design",
        "bio": "Creative professional with expertise...",
        "image": "https://s3.amazonaws.com/bucket/team/jane-smith.jpg",
        "skills": ["Design", "Photography"],
        "experience": "7 years",
        "isActive": true,
        "sortOrder": 2,
        "createdAt": "2023-12-01T09:00:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

### 3. Get Team Member by ID
**GET** `/api/team/:id`

Retrieves a specific team member by MongoDB ObjectId.

**Parameters:**
- `id` - MongoDB ObjectId

**Response:**
```javascript
{
  "success": true,
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "John Doe",
    "position": "Event Manager",
    "department": "Operations",
    "bio": "Experienced event manager with 5+ years of expertise in planning and executing large-scale events. Specializes in corporate events, weddings, and social gatherings.",
    "email": "<EMAIL>",
    "phone": "9876543210",
    "image": "https://s3.amazonaws.com/bucket/team/john-doe.jpg",
    "socialLinks": {
      "linkedin": "https://linkedin.com/in/johndoe",
      "twitter": "https://twitter.com/johndoe",
      "instagram": "https://instagram.com/johndoe",
      "facebook": "https://facebook.com/johndoe"
    },
    "skills": [
      "Event Planning",
      "Team Management", 
      "Budget Management",
      "Vendor Coordination",
      "Client Relations"
    ],
    "experience": "5 years",
    "education": "MBA in Event Management from Mumbai University",
    "achievements": [
      "Best Event Manager 2023",
      "Successfully managed 100+ events",
      "Client satisfaction rate of 98%",
      "Led team of 15+ professionals"
    ],
    "isActive": true,
    "sortOrder": 1,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

### 4. Update Team Member
**PUT** `/api/team/:id`

Updates an existing team member. Supports both JSON and multipart/form-data.

**Content-Type:** `multipart/form-data` or `application/json`

**Parameters:**
- `id` - MongoDB ObjectId

**Form Fields:** (All optional)
```javascript
{
  "name": "John Doe Updated",
  "position": "Senior Event Manager",
  "department": "Operations",
  "bio": "Updated bio with more experience...",
  "email": "<EMAIL>",
  "phone": "9876543211",
  "socialLinks": {
    "linkedin": "https://linkedin.com/in/johndoe-updated",
    "twitter": "https://twitter.com/johndoe_updated"
  },
  "skills": ["Event Planning", "Team Leadership", "Strategic Planning"],
  "experience": "6 years",
  "education": "MBA in Event Management, Certified Event Planner",
  "achievements": [
    "Best Event Manager 2023 & 2024",
    "Successfully managed 150+ events"
  ],
  "isActive": true,
  "sortOrder": 1
}

// File upload (optional):
image: File                    // New profile image
```

**Response:**
```javascript
{
  "success": true,
  "message": "Team member updated successfully",
  "data": {
    // Updated team member object
  }
}
```

### 5. Delete Team Member
**DELETE** `/api/team/:id`

Soft deletes a team member (sets isActive: false).

**Parameters:**
- `id` - MongoDB ObjectId

**Response:**
```javascript
{
  "success": true,
  "message": "Team member deleted successfully",
  "data": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "John Doe",
    "isActive": false,
    "updatedAt": "2023-12-01T11:00:00.000Z"
  }
}
```

---

## 🚨 **Error Responses**

### Validation Error (400)
```javascript
{
  "success": false,
  "message": "Validation error",
  "errors": [
    "Name is required",
    "Name cannot exceed 100 characters",
    "Please enter a valid email address",
    "Phone number must be 10 digits"
  ]
}
```

### Image Required (400)
```javascript
{
  "success": false,
  "message": "Image is required"
}
```

### Not Found (404)
```javascript
{
  "success": false,
  "message": "Team member not found"
}
```

### Server Error (500)
```javascript
{
  "success": false,
  "message": "Failed to create team member",
  "debug": "Detailed error message (development only)"
}
```

---

## 📝 **Usage Examples**

### Frontend Integration
```javascript
// Create team member with image
const formData = new FormData();
formData.append('name', 'John Doe');
formData.append('position', 'Event Manager');
formData.append('department', 'Operations');
formData.append('bio', 'Experienced event manager...');
formData.append('email', '<EMAIL>');
formData.append('phone', '9876543210');
formData.append('skills', JSON.stringify(['Event Planning', 'Team Management']));
formData.append('experience', '5 years');
formData.append('sortOrder', '1');
formData.append('image', profileImageFile);

const response = await fetch('/api/team', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Get team members with filtering
const teamMembers = await fetch('/api/team?department=Operations&sortBy=sortOrder&sortOrder=asc&page=1&limit=10', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Update team member
const updateData = {
  position: 'Senior Event Manager',
  experience: '6 years'
};

const updateResponse = await fetch(`/api/team/${memberId}`, {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(updateData)
});
```

### Admin Features
- ✅ Create unlimited team member profiles
- ✅ Upload profile images for each member
- ✅ Comprehensive member information management
- ✅ Social media links integration
- ✅ Skills and achievements tracking
- ✅ Department and position organization
- ✅ Custom sorting and ordering
- ✅ Advanced filtering and search
- ✅ Contact information management

---

## 🔧 **Technical Notes**

- **File Upload**: Uses AWS S3 for image storage
- **Image Types**: Supports JPG, PNG, GIF, WebP
- **File Size**: Maximum 5MB per image
- **Required Image**: Profile image is mandatory for team members
- **Database**: MongoDB with ObjectId
- **Validation**: Joi schema validation
- **Security**: JWT authentication required
- **Soft Delete**: Team members are never permanently deleted
- **Search**: Full-text search in name, position, and department
- **Social Links**: Supports LinkedIn, Twitter, Instagram, Facebook
- **Skills Array**: Flexible skills management
- **Achievements**: Multiple achievements per member

---

## 🎯 **Business Logic**

### Admin Capabilities
- **Unlimited Creation**: Admin can create multiple team member profiles
- **No Duplicate Restrictions**: Same names allowed for different team members
- **Flexible Organization**: Custom departments and positions
- **Profile Management**: Complete control over member information
- **Image Management**: Easy profile image updates
- **Status Control**: Activate/deactivate members as needed

### Display Features
- **Sorted Display**: Members displayed by sortOrder (ascending)
- **Department Grouping**: Filter and group by departments
- **Search Functionality**: Quick member lookup
- **Social Integration**: Direct links to social profiles
- **Professional Profiles**: Complete professional information display

---

*Last Updated: December 2024*
