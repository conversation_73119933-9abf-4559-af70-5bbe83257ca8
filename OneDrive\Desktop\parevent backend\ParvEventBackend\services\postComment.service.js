const PostComment = require("../models/postComment.model");

// Create Post Comment
const createPostComment = async (commentData, req = null) => {
  try {
    // Add IP address and user agent if request is available
    if (req) {
      commentData.ipAddress = req.ip || req.connection.remoteAddress;
      commentData.userAgent = req.get('User-Agent');
    }
    
    const comment = new PostComment(commentData);
    const savedComment = await comment.save();
    return savedComment;
  } catch (error) {
    throw new Error(error.message || "Failed to create comment");
  }
};

// Get all Post Comments with filtering and pagination
const getAllPostComments = async (filters = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      isVisible,
      blogId,
      search,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = filters;

    // Build query
    const query = {};

    if (status) {
      query.status = status;
    }

    if (isVisible !== undefined) {
      query.isVisible = isVisible === 'true';
    }

    if (blogId) {
      // Handle both Number (auto-increment) and ObjectId
      if (blogId.match(/^[0-9a-fA-F]{24}$/)) {
        // It's a MongoDB ObjectId
        query.blogId = blogId;
      } else {
        // It's a Number (auto-increment)
        query.blogId = parseInt(blogId);
      }
    }

    if (search) {
      query.$text = { $search: search };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    // Execute query with pagination
    const comments = await PostComment.find(query)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await PostComment.countDocuments(query);

    return {
      comments,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw new Error(error.message || "Failed to fetch comments");
  }
};

// Get Post Comment by ID
const getPostCommentById = async (id) => {
  try {
    const comment = await PostComment.findOne({ id: parseInt(id) });
    if (!comment) {
      throw new Error("Comment not found");
    }
    return comment;
  } catch (error) {
    throw new Error(error.message || "Failed to fetch comment");
  }
};

// Update Post Comment
const updatePostComment = async (id, updateData) => {
  try {
    // If status is being updated, add moderation info
    if (updateData.status && updateData.status !== 'pending') {
      updateData.moderatedAt = new Date();
      if (updateData.status === 'approved') {
        updateData.isVisible = true;
      } else {
        updateData.isVisible = false;
      }
    }
    
    const comment = await PostComment.findOneAndUpdate(
      { id: parseInt(id) },
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!comment) {
      throw new Error("Comment not found");
    }
    
    return comment;
  } catch (error) {
    throw new Error(error.message || "Failed to update comment");
  }
};

// Delete Post Comment
const deletePostComment = async (id) => {
  try {
    const comment = await PostComment.findOneAndDelete({ id: parseInt(id) });
    if (!comment) {
      throw new Error("Comment not found");
    }
    return { message: "Comment deleted successfully" };
  } catch (error) {
    throw new Error(error.message || "Failed to delete comment");
  }
};

// Get approved comments (for public display)
const getApprovedComments = async (filters = {}) => {
  try {
    const { sortBy = "createdAt", sortOrder = "desc", limit = 50, blogId } = filters;

    const query = { status: "approved", isVisible: true };

    if (blogId) {
      // Handle both Number (auto-increment) and ObjectId
      if (blogId.match(/^[0-9a-fA-F]{24}$/)) {
        // It's a MongoDB ObjectId
        query.blogId = blogId;
      } else {
        // It's a Number (auto-increment)
        query.blogId = parseInt(blogId);
      }
    }

    const sort = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    const comments = await PostComment.find(query)
      .sort(sort)
      .limit(parseInt(limit))
      .select('name comment createdAt blogId')
      .lean();

    return comments;
  } catch (error) {
    throw new Error(error.message || "Failed to fetch approved comments");
  }
};

// Get comments by blog ID (for specific blog)
const getCommentsByBlogId = async (blogId, filters = {}) => {
  try {
    const {
      status = "approved",
      isVisible = true,
      sortBy = "createdAt",
      sortOrder = "desc",
      page = 1,
      limit = 20
    } = filters;

    // Handle both Number (auto-increment) and ObjectId
    let blogIdValue;
    if (blogId.match(/^[0-9a-fA-F]{24}$/)) {
      // It's a MongoDB ObjectId
      blogIdValue = blogId;
    } else {
      // It's a Number (auto-increment)
      blogIdValue = parseInt(blogId);
    }

    const query = {
      blogId: blogIdValue,
      status,
      isVisible
    };

    const sort = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    const comments = await PostComment.find(query)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .select('name comment createdAt')
      .lean();

    // Get total count for pagination
    const total = await PostComment.countDocuments(query);

    return {
      comments,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw new Error(error.message || "Failed to fetch comments for blog");
  }
};

// Get comment count by blog ID
const getCommentCountByBlogId = async (blogId) => {
  try {
    // Handle both Number (auto-increment) and ObjectId
    let blogIdValue;
    if (blogId.match(/^[0-9a-fA-F]{24}$/)) {
      // It's a MongoDB ObjectId
      blogIdValue = blogId;
    } else {
      // It's a Number (auto-increment)
      blogIdValue = parseInt(blogId);
    }

    const count = await PostComment.countDocuments({
      blogId: blogIdValue,
      status: "approved",
      isVisible: true
    });

    return { blogId: blogIdValue, commentCount: count };
  } catch (error) {
    throw new Error(error.message || "Failed to fetch comment count for blog");
  }
};

// Get comment statistics
const getCommentStatistics = async () => {
  try {
    const totalComments = await PostComment.countDocuments();
    const pendingComments = await PostComment.countDocuments({ status: "pending" });
    const approvedComments = await PostComment.countDocuments({ status: "approved" });
    const rejectedComments = await PostComment.countDocuments({ status: "rejected" });
    const spamComments = await PostComment.countDocuments({ status: "spam" });
    
    const recentComments = await PostComment.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name email comment status createdAt')
      .lean();

    return {
      totalComments,
      pendingComments,
      approvedComments,
      rejectedComments,
      spamComments,
      recentComments
    };
  } catch (error) {
    throw new Error(error.message || "Failed to fetch comment statistics");
  }
};

module.exports = {
  createPostComment,
  getAllPostComments,
  getPostCommentById,
  updatePostComment,
  deletePostComment,
  getApprovedComments,
  getCommentsByBlogId,
  getCommentCountByBlogId,
  getCommentStatistics
};
