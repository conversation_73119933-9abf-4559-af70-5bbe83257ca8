$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTYxNzE5LCJleHAiOjE3NTExNjUzMTl9.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'
}

Write-Host "Testing Basic Endpoints"

# Test GET endpoints
Write-Host "`nTesting GET endpoints..."
$endpoints = @(
    'http://localhost:8005/api/reviews',
    'http://localhost:8005/api/team',
    'http://localhost:8005/api/gallery',
    'http://localhost:8005/api/hero-section'
)

foreach ($endpoint in $endpoints) {
    $name = ($endpoint -split '/')[-1]
    try {
        $result = Invoke-RestMethod -Uri $endpoint -Headers $headers
        Write-Host "SUCCESS: $name - Found $($result.data.PSObject.Properties.Value.Count) items"
    } catch {
        Write-Host "ERROR: $name failed"
    }
}

Write-Host "`nBasic endpoint test complete"
