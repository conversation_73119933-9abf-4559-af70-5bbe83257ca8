const postCommentService = require("../services/postComment.service");

// Create Post Comment
const createPostComment = async (req, res) => {
  try {
    const { name, email, comment, blogId } = req.body;

    if (!name || !email || !comment || !blogId) {
      return res.status(400).json({
        status: false,
        code: 400,
        message: "Name, email, comment, and blog ID are required",
        data: null
      });
    }

    const commentData = req.body;
    const result = await postCommentService.createPostComment(commentData, req);

    res.status(201).json({
      status: true,
      code: 201,
      message: "Comment submitted successfully and is pending moderation",
      data: result
    });
  } catch (error) {
    console.error("Error creating comment:", error);
    res.status(400).json({
      status: false,
      code: 400,
      message: error.message || "Failed to create comment",
      data: null
    });
  }
};

// Get all Post Comments (Admin)
const getAllPostComments = async (req, res) => {
  try {
    const filters = {
      page: req.query.page,
      limit: req.query.limit,
      status: req.query.status,
      isVisible: req.query.isVisible,
      blogId: req.query.blogId,
      search: req.query.search,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await postCommentService.getAllPostComments(filters);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Comments retrieved successfully",
      data: result.comments,
      pagination: result.pagination
    });
  } catch (error) {
    console.error("Error fetching comments:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch comments",
      data: null
    });
  }
};

// Get Post Comment by ID
const getPostCommentById = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await postCommentService.getPostCommentById(id);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Comment retrieved successfully",
      data: result
    });
  } catch (error) {
    console.error("Error fetching comment:", error);
    const statusCode = error.message === "Comment not found" ? 404 : 500;
    res.status(statusCode).json({
      status: false,
      code: statusCode,
      message: error.message || "Failed to fetch comment",
      data: null
    });
  }
};

// Update Post Comment (Admin - for moderation)
const updatePostComment = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // Add moderator info if available
    if (req.user && req.user.email) {
      updateData.moderatedBy = req.user.email;
    }
    
    const result = await postCommentService.updatePostComment(id, updateData);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Comment updated successfully",
      data: result
    });
  } catch (error) {
    console.error("Error updating comment:", error);
    const statusCode = error.message === "Comment not found" ? 404 : 400;
    res.status(statusCode).json({
      status: false,
      code: statusCode,
      message: error.message || "Failed to update comment",
      data: null
    });
  }
};

// Delete Post Comment
const deletePostComment = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await postCommentService.deletePostComment(id);

    res.status(200).json({
      status: true,
      code: 200,
      message: result.message,
      data: null
    });
  } catch (error) {
    console.error("Error deleting comment:", error);
    const statusCode = error.message === "Comment not found" ? 404 : 500;
    res.status(statusCode).json({
      status: false,
      code: statusCode,
      message: error.message || "Failed to delete comment",
      data: null
    });
  }
};

// Get approved comments (Public API)
const getApprovedComments = async (req, res) => {
  try {
    const filters = {
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder,
      limit: req.query.limit,
      blogId: req.query.blogId
    };

    const result = await postCommentService.getApprovedComments(filters);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Approved comments retrieved successfully",
      data: result
    });
  } catch (error) {
    console.error("Error fetching approved comments:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch approved comments",
      data: null
    });
  }
};

// Get comments by blog ID (Public)
const getCommentsByBlogId = async (req, res) => {
  try {
    const { blogId } = req.params;
    const filters = {
      status: req.query.status || "approved",
      isVisible: req.query.isVisible !== undefined ? req.query.isVisible : true,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder,
      page: req.query.page,
      limit: req.query.limit
    };

    const result = await postCommentService.getCommentsByBlogId(blogId, filters);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Blog comments retrieved successfully",
      data: result.comments,
      pagination: result.pagination
    });
  } catch (error) {
    console.error("Error fetching blog comments:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch blog comments",
      data: null
    });
  }
};

// Get comment count by blog ID (Public)
const getCommentCountByBlogId = async (req, res) => {
  try {
    const { blogId } = req.params;
    const result = await postCommentService.getCommentCountByBlogId(blogId);

    res.status(200).json({
      status: true,
      code: 200,
      message: "Comment count retrieved successfully",
      data: result
    });
  } catch (error) {
    console.error("Error fetching comment count:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch comment count",
      data: null
    });
  }
};

// Get comment statistics (Admin)
const getCommentStatistics = async (req, res) => {
  try {
    const result = await postCommentService.getCommentStatistics();

    res.status(200).json({
      status: true,
      code: 200,
      message: "Comment statistics retrieved successfully",
      data: result
    });
  } catch (error) {
    console.error("Error fetching comment statistics:", error);
    res.status(500).json({
      status: false,
      code: 500,
      message: error.message || "Failed to fetch comment statistics",
      data: null
    });
  }
};

module.exports = {
  createPostComment,
  getAllPostComments,
  getPostCommentById,
  updatePostComment,
  deletePostComment,
  getApprovedComments,
  getCommentsByBlogId,
  getCommentCountByBlogId,
  getCommentStatistics
};
