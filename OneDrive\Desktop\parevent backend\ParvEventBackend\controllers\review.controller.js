const Review = require('../models/review.model');
const { StatusCodes } = require('http-status-codes');
const Joi = require('joi');

// Validation schemas
const reviewSchema = Joi.object({
    name: Joi.string()
        .required()
        .max(100)
        .messages({
            'string.max': 'Name cannot exceed 100 characters',
            'any.required': 'Name is required'
        }),
    relationship: Joi.string()
        .required()
        .valid('bride', 'groom', 'parent', 'friend', 'relative', 'colleague', 'client', 'other')
        .messages({
            'any.required': 'Relationship is required',
            'any.only': 'Invalid relationship type'
        }),
    review: Joi.string()
        .required()
        .max(1000)
        .messages({
            'string.max': 'Review cannot exceed 1000 characters',
            'any.required': 'Review text is required'
        }),
    star: Joi.number()
        .integer()
        .min(1)
        .max(5)
        .required()
        .messages({
            'number.min': 'Star rating must be at least 1',
            'number.max': 'Star rating cannot exceed 5',
            'any.required': 'Star rating is required'
        }),

    sortOrder: Joi.number()
        .integer()
        .min(0)
        .default(0),
    isFeatured: Joi.boolean()
        .default(false),
    eventType: Joi.string()
        .valid('wedding', 'corporate', 'birthday', 'anniversary', 'other')
        .default('other')
});

const updateReviewSchema = Joi.object({
    name: Joi.string()
        .max(100)
        .messages({
            'string.max': 'Name cannot exceed 100 characters'
        }),
    relationship: Joi.string()
        .valid('bride', 'groom', 'parent', 'friend', 'relative', 'colleague', 'client', 'other')
        .messages({
            'any.only': 'Invalid relationship type'
        }),
    review: Joi.string()
        .max(1000)
        .messages({
            'string.max': 'Review cannot exceed 1000 characters'
        }),
    star: Joi.number()
        .integer()
        .min(1)
        .max(5)
        .messages({
            'number.min': 'Star rating must be at least 1',
            'number.max': 'Star rating cannot exceed 5'
        }),
    sortOrder: Joi.number()
        .integer()
        .min(0),
    isFeatured: Joi.boolean(),
    eventType: Joi.string()
        .valid('wedding', 'corporate', 'birthday', 'anniversary', 'other')
});

// Create review
const createReview = async (req, res) => {
    try {
        console.log('⭐ Creating review:', req.body);
        
        // Validate request body
        const { error, value } = reviewSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Create review data
        const reviewData = { ...value };

        // Add image if uploaded
        if (req.file) {
            reviewData.image = req.file.location;
        }

        const review = new Review(reviewData);
        await review.save();

        console.log('✅ Review created:', review._id);

        res.status(StatusCodes.CREATED).json({
            success: true,
            message: 'Review created successfully',
            data: review.getPublicData()
        });

    } catch (error) {
        console.error('❌ Create review error:', error);
        
        // Allow admin to create multiple reviews - no duplicate check needed

        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to create review',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get all reviews
const getAllReviews = async (req, res) => {
    try {
        console.log('📋 Fetching all reviews');
        
        const { 
            page = 1, 
            limit = 10, 
            star,
            relationship,
            eventType,
            featured,
            sortBy = 'sortOrder',
            sortOrder = 'asc'
        } = req.query;

        // Build filter
        const filter = { isActive: true };
        
        if (star) {
            filter.star = parseInt(star);
        }
        
        if (relationship && relationship !== 'all') {
            filter.relationship = relationship;
        }

        if (eventType && eventType !== 'all') {
            filter.eventType = eventType;
        }

        if (featured !== undefined) {
            filter.isFeatured = featured === 'true';
        }

        // Build sort
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query with pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        
        const [reviews, total, stats] = await Promise.all([
            Review.find(filter)
                .sort(sort)
                .skip(skip)
                .limit(parseInt(limit)),
            Review.countDocuments(filter),
            Review.getAverageRating()
        ]);

        const totalPages = Math.ceil(total / parseInt(limit));

        console.log(`✅ Found ${reviews.length} reviews`);

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                reviews: reviews.map(review => review.getPublicData()),
                stats,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalItems: total,
                    itemsPerPage: parseInt(limit),
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1
                }
            }
        });

    } catch (error) {
        console.error('❌ Get all reviews error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch reviews',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get review by ID
const getReviewById = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🔍 Fetching review by ID:', id);

        // Find by MongoDB ObjectId only
        let review = await Review.findOne({
            _id: id,
            isActive: true
        });

        if (!review) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Review not found'
            });
        }

        console.log('✅ Review found:', review._id);

        res.status(StatusCodes.OK).json({
            success: true,
            data: review.getPublicData()
        });

    } catch (error) {
        console.error('❌ Get review by ID error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch review',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Update review
const updateReview = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('📝 Updating review:', id);

        // Validate request body
        const { error, value } = updateReviewSchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Find review by ObjectId
        let review = await Review.findOne({
            _id: id,
            isActive: true
        });

        if (!review) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Review not found'
            });
        }

        // Update fields
        Object.keys(value).forEach(key => {
            review[key] = value[key];
        });

        // Update image if new one is uploaded
        if (req.file) {
            review.image = req.file.location;
        }

        await review.save();

        console.log('✅ Review updated:', review._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Review updated successfully',
            data: review.getPublicData()
        });

    } catch (error) {
        console.error('❌ Update review error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to update review',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Delete review
const deleteReview = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🗑️ Deleting review:', id);

        // Find and soft delete review
        const review = await Review.findOneAndUpdate(
            {
                _id: id,
                isActive: true
            },
            { isActive: false },
            { new: true }
        );

        if (!review) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Review not found'
            });
        }

        console.log('✅ Review deleted:', review._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Review deleted successfully'
        });

    } catch (error) {
        console.error('❌ Delete review error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to delete review',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get review statistics
const getReviewStats = async (req, res) => {
    try {
        console.log('📊 Fetching review statistics');

        const stats = await Review.getAverageRating();

        res.status(StatusCodes.OK).json({
            success: true,
            data: stats
        });

    } catch (error) {
        console.error('❌ Get review stats error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch review statistics',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

module.exports = {
    createReview,
    getAllReviews,
    getReviewById,
    updateReview,
    deleteReview,
    getReviewStats
};
