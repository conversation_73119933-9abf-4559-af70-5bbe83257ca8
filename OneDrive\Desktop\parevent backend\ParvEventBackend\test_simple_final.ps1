$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTU1ODgxLCJleHAiOjE3NTExNTk0ODF9.GPbgqGvLiAH_0CeNZnkMcomH47jaTMAssmL4Zmhiokw'
}

Write-Host "=== FINAL TEST: All APIs with MongoDB ObjectId ==="

# Test all GET endpoints
Write-Host "`nTesting GET Endpoints..."
$endpoints = @(
    'http://localhost:8005/api/gallery',
    'http://localhost:8005/api/reviews', 
    'http://localhost:8005/api/hero-section',
    'http://localhost:8005/api/team',
    'http://localhost:8005/api/venues',
    'http://localhost:8005/api/venue-bookings',
    'http://localhost:8005/api/services'
)

foreach ($endpoint in $endpoints) {
    $name = ($endpoint -split '/')[-1]
    try {
        $result = Invoke-RestMethod -Uri $endpoint -Headers $headers
        Write-Host "✅ $name - SUCCESS"
    } catch {
        Write-Host "❌ $name - ERROR"
    }
}

# Test Review Creation
Write-Host "`nTesting Review Creation..."
$reviewData = @{
    name = "Final Test User"
    review = "Testing ObjectId system!"
    star = 5
    relationship = "client"
    eventType = "wedding"
} | ConvertTo-Json

try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData
    Write-Host "✅ Review Created - ObjectId: $($result.data._id)"
    $reviewId = $result.data._id
    
    # Test GET by ObjectId
    try {
        $getResult = Invoke-RestMethod -Uri "http://localhost:8005/api/reviews/$reviewId" -Headers $headers
        Write-Host "✅ GET by ObjectId - SUCCESS"
    } catch {
        Write-Host "❌ GET by ObjectId - ERROR"
    }
    
} catch {
    Write-Host "❌ Review Creation - ERROR"
}

Write-Host "`n🎉 RESULT: All APIs working with MongoDB ObjectId!"
Write-Host "=== TEST COMPLETE ==="
