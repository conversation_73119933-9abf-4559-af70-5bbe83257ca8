$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTU1ODgxLCJleHAiOjE3NTExNTk0ODF9.GPbgqGvLiAH_0CeNZnkMcomH47jaTMAssmL4Zmhiokw'
}

Write-Host "Testing venue-bookings endpoint..."
try {
    $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/venue-bookings?page=1&limit=10' -Headers $headers
    Write-Host "SUCCESS: Venue bookings endpoint working!"
    Write-Host "Response: $($result | ConvertTo-Json -Depth 2)"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
}
