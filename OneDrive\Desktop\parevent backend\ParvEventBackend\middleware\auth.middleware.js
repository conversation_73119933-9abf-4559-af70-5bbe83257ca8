const jwt = require('jsonwebtoken');
const { StatusCodes } = require('http-status-codes');
const Admin = require('../models/admin.model');

class AuthMiddleware {
    // Verify JWT token and authenticate admin
    async authenticateAdmin(req, res, next) {
        try {
            let token;

            // Get token from Authorization header or cookie
            const authHeader = req.headers.authorization;
            if (authHeader && authHeader.startsWith('Bearer ')) {
                token = authHeader.substring(7);
            } else if (req.cookies && req.cookies.adminToken) {
                token = req.cookies.adminToken;
            }

            if (!token) {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Access denied. No token provided.'
                });
            }

            // Verify token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            // Check if token type is admin
            if (decoded.type !== 'admin') {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Access denied. Invalid token type.'
                });
            }

            // Find admin in database
            const admin = await Admin.findById(decoded.adminId);
            if (!admin) {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Access denied. Admin not found.'
                });
            }

            // Check if admin is active
            if (!admin.isActive) {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Access denied. Account is deactivated.'
                });
            }

            // Check if admin is verified
            if (!admin.isVerified) {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Access denied. Please verify your email first.'
                });
            }

            // Check if 2FA is verified (TEMPORARILY DISABLED FOR DEVELOPMENT)
            // TODO: Re-enable 2FA in production
            if (false && !admin.otpVerified) {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Access denied. Please complete 2FA setup first.'
                });
            }

            // Check if account is locked
            if (admin.isLocked) {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Access denied. Account is temporarily locked.'
                });
            }

            // Add admin info to request object
            req.admin = {
                adminId: admin._id,
                role: admin.role,
                email: admin.email
            };

            next();

        } catch (error) {
            console.error('Authentication error:', error);

            if (error.name === 'JsonWebTokenError') {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Access denied. Invalid token.'
                });
            }

            if (error.name === 'TokenExpiredError') {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    success: false,
                    message: 'Access denied. Token has expired.'
                });
            }

            return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                success: false,
                message: 'Authentication failed.'
            });
        }
    }

    // Optional authentication - doesn't fail if no token
    async optionalAuth(req, res, next) {
        try {
            let token;

            // Get token from Authorization header or cookie
            const authHeader = req.headers.authorization;
            if (authHeader && authHeader.startsWith('Bearer ')) {
                token = authHeader.substring(7);
            } else if (req.cookies && req.cookies.adminToken) {
                token = req.cookies.adminToken;
            }

            if (!token) {
                return next();
            }

            // Verify token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            if (decoded.type === 'admin') {
                const admin = await Admin.findById(decoded.adminId);
                if (admin && admin.isActive && admin.isVerified && !admin.isLocked) {
                    req.admin = {
                        adminId: admin._id,
                        role: admin.role,
                        email: admin.email
                    };
                }
            }

            next();

        } catch (error) {
            // For optional auth, we don't fail on token errors
            next();
        }
    }

    // Check if user is admin (use after authenticateAdmin)
    requireAdmin(req, res, next) {
        if (!req.admin || req.admin.role !== 'admin') {
            return res.status(StatusCodes.FORBIDDEN).json({
                success: false,
                message: 'Access denied. Admin privileges required.'
            });
        }
        next();
    }

    // Rate limiting for authentication endpoints
    createRateLimit() {
        const attempts = new Map();
        const maxAttempts = 10; // Increased to 10 attempts
        const windowMs = 15 * 60 * 1000; // 15 minutes

        return (req, res, next) => {
            const ip = req.ip || req.connection.remoteAddress;
            const now = Date.now();
            
            // Clean up old entries
            for (const [key, data] of attempts.entries()) {
                if (now - data.firstAttempt > windowMs) {
                    attempts.delete(key);
                }
            }

            const attemptData = attempts.get(ip);
            
            if (!attemptData) {
                attempts.set(ip, {
                    count: 1,
                    firstAttempt: now
                });
                return next();
            }

            if (now - attemptData.firstAttempt > windowMs) {
                attempts.set(ip, {
                    count: 1,
                    firstAttempt: now
                });
                return next();
            }

            if (attemptData.count >= maxAttempts) {
                return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
                    success: false,
                    message: 'Too many authentication attempts. Please try again later.'
                });
            }

            attemptData.count++;
            next();
        };
    }

    // Validate request origin
    validateOrigin(req, res, next) {
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:5173',
            'http://localhost:5174',
            'http://localhost:8080',
            process.env.FRONTEND_URL
        ].filter(Boolean);

        const origin = req.headers.origin;

        console.log('🔍 Origin validation - Request origin:', origin);
        console.log('📋 Allowed origins:', allowedOrigins);

        // Allow requests with no origin (like Postman, mobile apps)
        if (!origin) {
            console.log('✅ No origin header - allowing request');
            return next();
        }

        if (!allowedOrigins.includes(origin)) {
            console.log('❌ Origin not in allowed list');
            return res.status(StatusCodes.FORBIDDEN).json({
                success: false,
                message: 'Access denied. Invalid origin.',
                debug: {
                    requestOrigin: origin,
                    allowedOrigins: allowedOrigins
                }
            });
        }

        console.log('✅ Origin validation passed');
        next();
    }

    // Security headers middleware
    securityHeaders(req, res, next) {
        // Set security headers
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-XSS-Protection', '1; mode=block');
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
        
        if (process.env.NODE_ENV === 'production') {
            res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        }

        next();
    }
}

module.exports = new AuthMiddleware();
