const mongoose = require('mongoose');

const venueSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Name is required'],
        trim: true,
        maxlength: [200, 'Name cannot exceed 200 characters']
    },
    image: {
        type: String,
        required: [true, 'Image is required']
    },
    venueType: {
        type: String,
        required: [true, 'Venue type is required'],
        enum: ['banquet-hall', 'outdoor', 'resort', 'hotel', 'farmhouse', 'palace', 'garden', 'beach', 'other'],
        default: 'banquet-hall'
    },
    location: {
        type: String,
        required: [true, 'Location is required'],
        trim: true,
        maxlength: [500, 'Location cannot exceed 500 characters']
    },
    capacity: {
        type: Number,
        required: [true, 'Capacity is required'],
        min: [1, 'Capacity must be at least 1'],
        max: [15000, 'Capacity cannot exceed 15000']
    },
    seats: {
        type: Number,
        required: [true, 'Seats is required'],
        min: [1, 'Seats must be at least 1'],
        max: [10000, 'Seats cannot exceed 10000']
    },
    isActive: {
        type: Boolean,
        default: true
    },
    sortOrder: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});

// Index for better performance
venueSchema.index({ venueType: 1, isActive: 1 });
venueSchema.index({ capacity: 1 });
venueSchema.index({ seats: 1 });
venueSchema.index({ sortOrder: 1 });

// Method to get public data
venueSchema.methods.getPublicData = function() {
    return {
        _id: this._id,
        name: this.name,
        image: this.image,
        venueType: this.venueType,
        location: this.location,
        capacity: this.capacity,
        seats: this.seats,
        isActive: this.isActive,
        sortOrder: this.sortOrder,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt
    };
};

module.exports = mongoose.model('Venue', venueSchema);
