$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiNjg2MDgxMWZmMDcyYjliYmJhMDlkN2IzIiwidHlwZSI6ImFkbWluIiwiaWF0IjoxNzUxMTYxMzk3LCJleHAiOjE3NTExNjQ5OTd9.QEYGEFai2070jIVyqtSL26e40KGUNNyropKf9nR-U_c'
}

Write-Host "Testing Admin Multiple Creation"
Write-Host "==============================="

# Test Multiple Review Creation
Write-Host "`nTesting Multiple Reviews..."
$reviewCount = 0
for ($i = 1; $i -le 3; $i++) {
    $reviewData = @{
        name = "Test User $i"
        review = "Test review number $i"
        star = 5
        relationship = "client"
        eventType = "wedding"
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/reviews' -Method POST -Headers $headers -ContentType 'application/json' -Body $reviewData
        Write-Host "SUCCESS: Review $i created - ID: $($result.data._id)"
        $reviewCount++
    } catch {
        Write-Host "ERROR: Review $i failed"
    }
}
Write-Host "Reviews created: $reviewCount/3"

# Test Multiple Team Member Creation
Write-Host "`nTesting Multiple Team Members..."
$teamCount = 0
for ($i = 1; $i -le 3; $i++) {
    $teamData = @{
        name = "Team Member $i"
        sortOrder = $i
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri 'http://localhost:8005/api/team' -Method POST -Headers $headers -ContentType 'application/json' -Body $teamData
        Write-Host "SUCCESS: Team Member $i created - ID: $($result.data._id)"
        $teamCount++
    } catch {
        Write-Host "ERROR: Team Member $i failed"
    }
}
Write-Host "Team members created: $teamCount/3"

Write-Host "`nFINAL RESULTS:"
Write-Host "Reviews: $reviewCount/3 created"
Write-Host "Team Members: $teamCount/3 created"
Write-Host "Test Complete!"
