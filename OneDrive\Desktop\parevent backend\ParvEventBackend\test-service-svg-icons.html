<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service SVG Icons Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .service-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 50%;
            padding: 15px;
        }

        .service-svg {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .service-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .service-card:hover .service-svg {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .loading {
            text-align: center;
            padding: 2rem;
        }

        .error {
            color: #dc3545;
            text-align: center;
            padding: 1rem;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-5">🛠️ Service SVG Icons Test</h1>
                <p class="text-center text-muted mb-4">
                    Testing SVG icon display from Service API
                </p>
            </div>
        </div>

        <div id="loading" class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading services...</span>
            </div>
            <p class="mt-2">Loading services with SVG icons...</p>
        </div>

        <div id="error" class="error d-none">
            <div class="alert alert-danger">
                <h5>❌ Error Loading Services</h5>
                <p id="error-message">Failed to load services</p>
            </div>
        </div>

        <div id="services-container" class="row d-none">
            <!-- Services will be loaded here -->
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📋 SVG Icon Information</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><strong>✅ File Format:</strong> SVG (Scalable Vector Graphics)</li>
                            <li><strong>📁 Storage Location:</strong> S3 bucket in <code>services/icons/</code> folder</li>
                            <li><strong>📏 File Size Limit:</strong> 2MB maximum</li>
                            <li><strong>🎯 MIME Types:</strong> <code>image/svg+xml</code>, <code>image/svg</code></li>
                            <li><strong>🔧 Benefits:</strong> Scalable, lightweight, customizable, retina-ready</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API base URL - adjust if needed
        const API_BASE = '';

        // Fetch services from API
        async function fetchServices() {
            try {
                const response = await fetch(`${API_BASE}/services?limit=20&sortBy=sortOrder&sortOrder=asc`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || 'Failed to fetch services');
                }
                
                return result.data.services;
            } catch (error) {
                console.error('Error fetching services:', error);
                throw error;
            }
        }

        // Display services with SVG icons
        function displayServices(services) {
            const container = document.getElementById('services-container');
            
            if (!services || services.length === 0) {
                container.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <h5>📝 No Services Found</h5>
                            <p>No services are currently available. Please add some services first.</p>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = services.map(service => `
                <div class="col-md-4 col-sm-6 mb-4">
                    <div class="card service-card h-100">
                        <div class="card-body text-center">
                            <div class="service-icon mb-3">
                                <img src="${service.icons}" 
                                     alt="${service.title} Icon" 
                                     class="service-svg"
                                     onerror="handleIconError(this)"
                                     onload="handleIconLoad(this)">
                            </div>
                            <h5 class="card-title">${service.title}</h5>
                            <p class="card-text">${service.description}</p>
                            ${service.description2 ? `<p class="card-text"><small class="text-muted">${service.description2}</small></p>` : ''}
                            <div class="mt-auto">
                                <small class="text-muted">Sort Order: ${service.sortOrder}</small>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Handle icon loading success
        function handleIconLoad(img) {
            console.log('✅ SVG icon loaded successfully:', img.src);
            img.style.opacity = '1';
        }

        // Handle icon loading error
        function handleIconError(img) {
            console.error('❌ Failed to load SVG icon:', img.src);
            img.style.display = 'none';
            img.parentElement.innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <br><small>Icon not available</small>
                </div>
            `;
        }

        // Show error message
        function showError(message) {
            document.getElementById('loading').classList.add('d-none');
            document.getElementById('error').classList.remove('d-none');
            document.getElementById('error-message').textContent = message;
        }

        // Show services
        function showServices(services) {
            document.getElementById('loading').classList.add('d-none');
            document.getElementById('services-container').classList.remove('d-none');
            displayServices(services);
        }

        // Load services on page load
        async function loadServices() {
            try {
                console.log('🔄 Loading services...');
                const services = await fetchServices();
                console.log(`✅ Loaded ${services.length} services`);
                showServices(services);
            } catch (error) {
                console.error('❌ Error loading services:', error);
                showError(error.message);
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', loadServices);
    </script>
</body>
</html>
