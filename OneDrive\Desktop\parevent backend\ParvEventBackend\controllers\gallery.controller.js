const Gallery = require('../models/gallery.model');
const { StatusCodes } = require('http-status-codes');
const Joi = require('joi');

// Validation schemas
const gallerySchema = Joi.object({
    title: Joi.string()
        .required()
        .max(100)
        .messages({
            'string.max': 'Title cannot exceed 100 characters',
            'any.required': 'Title is required'
        }),
    description: Joi.string()
        .max(500)
        .allow('')
        .messages({
            'string.max': 'Description cannot exceed 500 characters'
        }),
    keywords: Joi.alternatives()
        .try(
            Joi.array().items(Joi.string().trim()),
            Joi.string().custom((value, helpers) => {
                // Convert comma-separated string to array
                if (typeof value === 'string') {
                    return value.split(',').map(keyword => keyword.trim()).filter(keyword => keyword.length > 0);
                }
                return value;
            })
        )
        .default([]),
    category: Joi.string()
        .valid('wedding', 'corporate', 'birthday', 'anniversary', 'other')
        .default('other'),

    sortOrder: Joi.number()
        .integer()
        .min(0)
        .default(0)
});

const updateGallerySchema = Joi.object({
    title: Joi.string()
        .max(100)
        .messages({
            'string.max': 'Title cannot exceed 100 characters'
        }),
    description: Joi.string()
        .max(500)
        .allow('')
        .messages({
            'string.max': 'Description cannot exceed 500 characters'
        }),
    keywords: Joi.alternatives()
        .try(
            Joi.array().items(Joi.string().trim()),
            Joi.string().custom((value, helpers) => {
                // Convert comma-separated string to array
                if (typeof value === 'string') {
                    return value.split(',').map(keyword => keyword.trim()).filter(keyword => keyword.length > 0);
                }
                return value;
            })
        ),
    category: Joi.string()
        .valid('wedding', 'corporate', 'birthday', 'anniversary', 'other'),
    sortOrder: Joi.number()
        .integer()
        .min(0)
});

// Create gallery item
const createGallery = async (req, res) => {
    try {
        console.log('📸 Creating gallery item:', req.body);
        
        // Validate request body
        const { error, value } = gallerySchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Check if image is uploaded
        if (!req.file) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Image is required'
            });
        }

        // Create gallery item
        const galleryData = {
            ...value,
            image: req.file.location
        };

        const gallery = new Gallery(galleryData);
        await gallery.save();

        console.log('✅ Gallery item created:', gallery._id);

        res.status(StatusCodes.CREATED).json({
            success: true,
            message: 'Gallery item created successfully',
            data: gallery.getPublicData()
        });

    } catch (error) {
        console.error('❌ Create gallery error:', error);
        
        // Allow admin to create multiple gallery items - no duplicate check needed

        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to create gallery item',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get all gallery items
const getAllGallery = async (req, res) => {
    try {
        console.log('📋 Fetching all gallery items');
        
        const { 
            page = 1, 
            limit = 10, 
            category, 
            keyword,
            sortBy = 'sortOrder',
            sortOrder = 'asc'
        } = req.query;

        // Build filter
        const filter = { isActive: true };
        
        if (category && category !== 'all') {
            filter.category = category;
        }
        
        if (keyword) {
            filter.keywords = { $in: [new RegExp(keyword, 'i')] };
        }

        // Build sort
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query with pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        
        const [galleries, total] = await Promise.all([
            Gallery.find(filter)
                .sort(sort)
                .skip(skip)
                .limit(parseInt(limit)),
            Gallery.countDocuments(filter)
        ]);

        const totalPages = Math.ceil(total / parseInt(limit));

        console.log(`✅ Found ${galleries.length} gallery items`);

        res.status(StatusCodes.OK).json({
            success: true,
            data: {
                galleries: galleries.map(gallery => gallery.getPublicData()),
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalItems: total,
                    itemsPerPage: parseInt(limit),
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1
                }
            }
        });

    } catch (error) {
        console.error('❌ Get all gallery error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch gallery items',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Get gallery by ID
const getGalleryById = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🔍 Fetching gallery by ID:', id);

        // Find by MongoDB ObjectId only
        let gallery = await Gallery.findOne({
            _id: id,
            isActive: true
        });

        if (!gallery) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Gallery item not found'
            });
        }

        console.log('✅ Gallery item found:', gallery._id);

        res.status(StatusCodes.OK).json({
            success: true,
            data: gallery.getPublicData()
        });

    } catch (error) {
        console.error('❌ Get gallery by ID error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to fetch gallery item',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Update gallery item
const updateGallery = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('📝 Updating gallery item:', id);

        // Validate request body
        const { error, value } = updateGallerySchema.validate(req.body);
        if (error) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Validation error',
                errors: error.details.map(detail => detail.message)
            });
        }

        // Find gallery item by ObjectId
        let gallery = await Gallery.findOne({
            _id: id,
            isActive: true
        });

        if (!gallery) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Gallery item not found'
            });
        }

        // Update fields
        Object.keys(value).forEach(key => {
            gallery[key] = value[key];
        });

        // Update image if new one is uploaded
        if (req.file) {
            gallery.image = req.file.location;
        }

        await gallery.save();

        console.log('✅ Gallery item updated:', gallery._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Gallery item updated successfully',
            data: gallery.getPublicData()
        });

    } catch (error) {
        console.error('❌ Update gallery error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to update gallery item',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

// Delete gallery item
const deleteGallery = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('🗑️ Deleting gallery item:', id);

        // Find and soft delete gallery item
        const gallery = await Gallery.findOneAndUpdate(
            {
                _id: id,
                isActive: true
            },
            { isActive: false },
            { new: true }
        );

        if (!gallery) {
            return res.status(StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Gallery item not found'
            });
        }

        console.log('✅ Gallery item deleted:', gallery._id);

        res.status(StatusCodes.OK).json({
            success: true,
            message: 'Gallery item deleted successfully'
        });

    } catch (error) {
        console.error('❌ Delete gallery error:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Failed to delete gallery item',
            ...(process.env.NODE_ENV === 'development' && { debug: error.message })
        });
    }
};

module.exports = {
    createGallery,
    getAllGallery,
    getGalleryById,
    updateGallery,
    deleteGallery
};
