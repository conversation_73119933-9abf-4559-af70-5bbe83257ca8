const FAQ = require("../models/faq.model");

// Create FAQ
const createFAQ = async (faqData) => {
  try {
    const faq = new FAQ(faqData);
    const savedFAQ = await faq.save();
    return savedFAQ;
  } catch (error) {
    throw new Error(error.message || "Failed to create FAQ");
  }
};

// Get all FAQs with filtering and pagination
const getAllFAQs = async (filters = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      status = "active",
      search,
      sortBy = "order",
      sortOrder = "asc"
    } = filters;

    // Build query
    const query = {};
    
    if (status) {
      query.status = status;
    }
    
    if (category) {
      query.category = category;
    }
    
    if (search) {
      query.$text = { $search: search };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    // Execute query with pagination
    const faqs = await FAQ.find(query)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await FAQ.countDocuments(query);

    return {
      faqs,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw new Error(error.message || "Failed to fetch FAQs");
  }
};

// Get FAQ by ID
const getFAQById = async (id) => {
  try {
    const faq = await FAQ.findOne({ id: parseInt(id) });
    if (!faq) {
      throw new Error("FAQ not found");
    }
    return faq;
  } catch (error) {
    throw new Error(error.message || "Failed to fetch FAQ");
  }
};

// Update FAQ
const updateFAQ = async (id, updateData) => {
  try {
    const faq = await FAQ.findOneAndUpdate(
      { id: parseInt(id) },
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!faq) {
      throw new Error("FAQ not found");
    }
    
    return faq;
  } catch (error) {
    throw new Error(error.message || "Failed to update FAQ");
  }
};

// Delete FAQ
const deleteFAQ = async (id) => {
  try {
    const faq = await FAQ.findOneAndDelete({ id: parseInt(id) });
    if (!faq) {
      throw new Error("FAQ not found");
    }
    return { message: "FAQ deleted successfully" };
  } catch (error) {
    throw new Error(error.message || "Failed to delete FAQ");
  }
};

// Get FAQs by category
const getFAQsByCategory = async (category, filters = {}) => {
  try {
    const { status = "active", sortBy = "order", sortOrder = "asc" } = filters;
    
    const query = { category, status };
    
    const sort = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;
    
    const faqs = await FAQ.find(query).sort(sort).lean();
    return faqs;
  } catch (error) {
    throw new Error(error.message || "Failed to fetch FAQs by category");
  }
};

// Get FAQ statistics
const getFAQStatistics = async () => {
  try {
    const totalFAQs = await FAQ.countDocuments();
    const activeFAQs = await FAQ.countDocuments({ status: "active" });
    const inactiveFAQs = await FAQ.countDocuments({ status: "inactive" });
    
    const categoriesStats = await FAQ.aggregate([
      { $match: { status: "active" } },
      { $group: { _id: "$category", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    return {
      totalFAQs,
      activeFAQs,
      inactiveFAQs,
      categoriesStats
    };
  } catch (error) {
    throw new Error(error.message || "Failed to fetch FAQ statistics");
  }
};

module.exports = {
  createFAQ,
  getAllFAQs,
  getFAQById,
  updateFAQ,
  deleteFAQ,
  getFAQsByCategory,
  getFAQStatistics
};
