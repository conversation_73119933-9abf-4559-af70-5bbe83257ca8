<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venue Booking Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .booking-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .info-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }
        .info-card .card-header {
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            font-weight: 600;
        }
        .status-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        .priority-badge {
            font-size: 0.8rem;
            padding: 0.3rem 0.8rem;
        }
        .detail-row {
            padding: 0.75rem 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        .detail-value {
            color: #212529;
        }
        .back-btn {
            background: #6c757d;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
        }
        .back-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        .status-actions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="booking-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-calendar-check me-3"></i>Venue Booking Details</h1>
                    <p class="mb-0">Complete booking information and status</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light back-btn" onclick="goBack()">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- Customer Information -->
            <div class="col-md-6">
                <div class="card info-card">
                    <div class="card-header">
                        <i class="fas fa-user me-2"></i>Customer Information
                    </div>
                    <div class="card-body">
                        <div class="detail-row">
                            <div class="row">
                                <div class="col-4 detail-label">Full Name:</div>
                                <div class="col-8 detail-value" id="fullName">-</div>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="row">
                                <div class="col-4 detail-label">Email:</div>
                                <div class="col-8 detail-value" id="email">-</div>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="row">
                                <div class="col-4 detail-label">Phone:</div>
                                <div class="col-8 detail-value" id="phoneNumber">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Information -->
            <div class="col-md-6">
                <div class="card info-card">
                    <div class="card-header">
                        <i class="fas fa-calendar me-2"></i>Booking Information
                    </div>
                    <div class="card-body">
                        <div class="detail-row">
                            <div class="row">
                                <div class="col-4 detail-label">Venue:</div>
                                <div class="col-8 detail-value" id="venueName">-</div>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="row">
                                <div class="col-4 detail-label">Date of Plan:</div>
                                <div class="col-8 detail-value" id="dateOfPlan">-</div>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="row">
                                <div class="col-4 detail-label">Booking Date:</div>
                                <div class="col-8 detail-value" id="createdAt">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status and Priority -->
        <div class="row">
            <div class="col-md-12">
                <div class="card info-card">
                    <div class="card-header">
                        <i class="fas fa-info-circle me-2"></i>Status & Priority
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="detail-row">
                                    <div class="row align-items-center">
                                        <div class="col-4 detail-label">Status:</div>
                                        <div class="col-8">
                                            <span class="badge status-badge" id="statusBadge">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="detail-row">
                                    <div class="row align-items-center">
                                        <div class="col-4 detail-label">Priority:</div>
                                        <div class="col-8">
                                            <span class="badge priority-badge" id="priorityBadge">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message -->
        <div class="row">
            <div class="col-md-12">
                <div class="card info-card">
                    <div class="card-header">
                        <i class="fas fa-message me-2"></i>Customer Message
                    </div>
                    <div class="card-body">
                        <div class="detail-value" id="message" style="line-height: 1.6;">-</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Update Actions (Admin Only) -->
        <div class="status-actions">
            <h5><i class="fas fa-cogs me-2"></i>Quick Actions</h5>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">Update Status:</label>
                    <select class="form-select" id="statusSelect">
                        <option value="new">New</option>
                        <option value="in-progress">In Progress</option>
                        <option value="resolved">Resolved</option>
                        <option value="closed">Closed</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Update Priority:</label>
                    <select class="form-select" id="prioritySelect">
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                    </select>
                </div>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary" onclick="updateBooking()">
                    <i class="fas fa-save me-2"></i>Update Booking
                </button>
                <button class="btn btn-success ms-2" onclick="contactCustomer()">
                    <i class="fas fa-phone me-2"></i>Contact Customer
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentBookingId = null;

        // Get booking ID from URL parameters
        function getBookingIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('id');
        }

        // Load booking details
        async function loadBookingDetails() {
            const bookingId = getBookingIdFromUrl();
            if (!bookingId) {
                alert('No booking ID provided');
                goBack();
                return;
            }

            currentBookingId = bookingId;

            try {
                const response = await fetch(`/venue-bookings/${bookingId}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch booking details');
                }

                const result = await response.json();
                if (result.success) {
                    displayBookingDetails(result.data);
                } else {
                    throw new Error(result.message || 'Failed to load booking');
                }
            } catch (error) {
                console.error('Error loading booking:', error);
                alert('Error loading booking details: ' + error.message);
            }
        }

        // Display booking details
        function displayBookingDetails(booking) {
            document.getElementById('fullName').textContent = booking.fullName || '-';
            document.getElementById('email').textContent = booking.email || '-';
            document.getElementById('phoneNumber').textContent = booking.phoneNumber || '-';
            document.getElementById('venueName').textContent = booking.venueName || '-';
            document.getElementById('dateOfPlan').textContent = formatDate(booking.dateOfPlan) || '-';
            document.getElementById('createdAt').textContent = formatDate(booking.createdAt) || '-';
            document.getElementById('message').textContent = booking.message || 'No message provided';

            // Set status badge
            const statusBadge = document.getElementById('statusBadge');
            statusBadge.textContent = booking.status || 'new';
            statusBadge.className = `badge status-badge ${getStatusClass(booking.status)}`;

            // Set priority badge
            const priorityBadge = document.getElementById('priorityBadge');
            priorityBadge.textContent = booking.priority || 'medium';
            priorityBadge.className = `badge priority-badge ${getPriorityClass(booking.priority)}`;

            // Set current values in selects
            document.getElementById('statusSelect').value = booking.status || 'new';
            document.getElementById('prioritySelect').value = booking.priority || 'medium';
        }

        // Get status badge class
        function getStatusClass(status) {
            switch(status) {
                case 'new': return 'bg-primary';
                case 'in-progress': return 'bg-warning';
                case 'resolved': return 'bg-success';
                case 'closed': return 'bg-secondary';
                default: return 'bg-primary';
            }
        }

        // Get priority badge class
        function getPriorityClass(priority) {
            switch(priority) {
                case 'low': return 'bg-info';
                case 'medium': return 'bg-primary';
                case 'high': return 'bg-warning';
                case 'urgent': return 'bg-danger';
                default: return 'bg-primary';
            }
        }

        // Format date
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Update booking status and priority
        async function updateBooking() {
            if (!currentBookingId) return;

            const status = document.getElementById('statusSelect').value;
            const priority = document.getElementById('prioritySelect').value;

            try {
                const response = await fetch(`/venue-bookings/${currentBookingId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ status, priority })
                });

                if (!response.ok) {
                    throw new Error('Failed to update booking');
                }

                const result = await response.json();
                if (result.success) {
                    alert('Booking updated successfully!');
                    loadBookingDetails(); // Reload to show updated data
                } else {
                    throw new Error(result.message || 'Failed to update booking');
                }
            } catch (error) {
                console.error('Error updating booking:', error);
                alert('Error updating booking: ' + error.message);
            }
        }

        // Contact customer
        function contactCustomer() {
            const phone = document.getElementById('phoneNumber').textContent;
            const email = document.getElementById('email').textContent;
            
            const action = confirm(`Contact customer?\n\nPhone: ${phone}\nEmail: ${email}\n\nClick OK to call, Cancel to email`);
            
            if (action) {
                // Open phone dialer
                window.open(`tel:${phone}`);
            } else {
                // Open email client
                window.open(`mailto:${email}?subject=Regarding your venue booking`);
            }
        }

        // Go back to list
        function goBack() {
            window.history.back();
        }

        // Load booking details when page loads
        document.addEventListener('DOMContentLoaded', loadBookingDetails);
    </script>
</body>
</html>
