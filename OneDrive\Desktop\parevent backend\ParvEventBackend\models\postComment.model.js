const mongoose = require("mongoose");
const AutoIncrement = require("mongoose-sequence")(mongoose);

const postCommentSchema = new mongoose.Schema(
  {
    id: { type: Number, unique: true },
    blogId: {
      type: mongoose.Schema.Types.Mixed,
      required: [true, "Blog ID is required"]
    },
    name: {
      type: String,
      required: [true, "Name is required"],
      trim: true,
      maxlength: [100, "Name cannot exceed 100 characters"]
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      trim: true,
      lowercase: true,
      validate: {
        validator: function(v) {
          return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
        },
        message: "Please enter a valid email address"
      }
    },
    comment: {
      type: String,
      required: [true, "Comment is required"],
      trim: true,
      maxlength: [1000, "Comment cannot exceed 1000 characters"]
    },
    status: {
      type: String,
      enum: {
        values: ["pending", "approved", "rejected", "spam"],
        message: "Status must be pending, approved, rejected, or spam"
      },
      default: "pending"
    },
    isVisible: {
      type: Boolean,
      default: false
    },
    moderatedBy: {
      type: String,
      default: null
    },
    moderatedAt: {
      type: Date,
      default: null
    },
    ipAddress: {
      type: String,
      default: null
    },
    userAgent: {
      type: String,
      default: null
    }
  },
  {
    timestamps: true
  }
);

// Index for better search performance
postCommentSchema.index({ name: 'text', email: 'text', comment: 'text' });
postCommentSchema.index({ email: 1 });
postCommentSchema.index({ status: 1 });
postCommentSchema.index({ isVisible: 1 });
postCommentSchema.index({ createdAt: -1 });
postCommentSchema.index({ blogId: 1 });

// Compound indexes for common queries
postCommentSchema.index({ blogId: 1, status: 1 });
postCommentSchema.index({ blogId: 1, isVisible: 1 });
postCommentSchema.index({ blogId: 1, createdAt: -1 });
postCommentSchema.index({ status: 1, isVisible: 1 });
postCommentSchema.index({ status: 1, createdAt: -1 });

postCommentSchema.plugin(AutoIncrement, { inc_field: "id", id: "post_comment_id_counter" });

module.exports = mongoose.model("PostComment", postCommentSchema);
