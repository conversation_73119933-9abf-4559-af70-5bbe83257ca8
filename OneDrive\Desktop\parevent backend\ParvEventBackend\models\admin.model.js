const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const CryptoJS = require('crypto-js');
const validator = require('validator');

const adminSchema = new mongoose.Schema({
    firstname: {
        type: String,
        required: [true, 'First name is required'],
        trim: true,
        minlength: [1, 'First name must be at least 1 character'],
        maxlength: [50, 'First name cannot exceed 50 characters']
    },
    lastname: {
        type: String,
        required: [true, 'Last name is required'],
        trim: true,
        minlength: [1, 'Last name must be at least 1 character'],
        maxlength: [50, 'Last name cannot exceed 50 characters']
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        lowercase: true,
        trim: true
    },
    password: {
        type: String,
        required: [true, 'Password is required'],
        minlength: [4, 'Password must be at least 4 characters']
    },
    confirmpassword: {
        type: String,
        validate: {
            validator: function(v) {
                // Only validate if confirmpassword is provided
                return !v || v === this.password;
            },
            message: 'Passwords do not match'
        }
    },
    isVerified: {
        type: Boolean,
        default: false
    },
    otpSecret: {
        type: String,
        default: null
    },
    otpVerified: {
        type: Boolean,
        default: false
    },
    loginAttempts: {
        type: Number,
        default: 0
    },
    lockUntil: {
        type: Date,
        default: null
    },
    lastLogin: {
        type: Date,
        default: null
    },
    role: {
        type: String,
        enum: ['admin'],
        default: 'admin'
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

// Virtual for account lock status
adminSchema.virtual('isLocked').get(function() {
    return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware to hash password and encrypt sensitive data
adminSchema.pre('save', async function(next) {
    try {
        console.log('🔐 Pre-save middleware: Processing admin data...');

        // Check if AES_SECRET_KEY is available
        if (!process.env.AES_SECRET_KEY) {
            throw new Error('AES_SECRET_KEY is not configured');
        }

        // Only hash password if it's modified
        if (this.isModified('password')) {
            console.log('🔒 Hashing password...');
            const saltRounds = 12;
            this.password = await bcrypt.hash(this.password, saltRounds);

            // Remove confirmpassword field before saving
            this.confirmpassword = undefined;
        }

        // Encrypt sensitive fields with AES256
        if (this.isModified('firstname')) {
            console.log('🔐 Encrypting firstname...');
            this.firstname = CryptoJS.AES.encrypt(this.firstname, process.env.AES_SECRET_KEY).toString();
        }

        if (this.isModified('lastname')) {
            console.log('🔐 Encrypting lastname...');
            this.lastname = CryptoJS.AES.encrypt(this.lastname, process.env.AES_SECRET_KEY).toString();
        }

        if (this.isModified('email')) {
            console.log('🔐 Encrypting email...');
            this.email = CryptoJS.AES.encrypt(this.email.toLowerCase(), process.env.AES_SECRET_KEY).toString();
        }

        console.log('✅ Pre-save processing completed');
        next();
    } catch (error) {
        console.error('❌ Pre-save middleware error:', error.message);
        next(error);
    }
});

// Method to decrypt sensitive fields
adminSchema.methods.getDecryptedData = function() {
    try {
        return {
            _id: this._id,
            firstname: CryptoJS.AES.decrypt(this.firstname, process.env.AES_SECRET_KEY).toString(CryptoJS.enc.Utf8),
            lastname: CryptoJS.AES.decrypt(this.lastname, process.env.AES_SECRET_KEY).toString(CryptoJS.enc.Utf8),
            email: CryptoJS.AES.decrypt(this.email, process.env.AES_SECRET_KEY).toString(CryptoJS.enc.Utf8),
            isVerified: this.isVerified,
            otpVerified: this.otpVerified,
            role: this.role,
            isActive: this.isActive,
            lastLogin: this.lastLogin,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    } catch (error) {
        throw new Error('Failed to decrypt admin data');
    }
};

// Method to compare password
adminSchema.methods.comparePassword = async function(candidatePassword) {
    try {
        return await bcrypt.compare(candidatePassword, this.password);
    } catch (error) {
        throw new Error('Password comparison failed');
    }
};

// Method to handle failed login attempts
adminSchema.methods.incLoginAttempts = function() {
    const maxAttempts = 10; // Increased to 10 attempts
    const lockTime = 30 * 60 * 1000; // 30 minutes

    // If we have a previous lock that has expired, restart at 1
    if (this.lockUntil && this.lockUntil < Date.now()) {
        return this.updateOne({
            $unset: { lockUntil: 1 },
            $set: { loginAttempts: 1 }
        });
    }
    
    const updates = { $inc: { loginAttempts: 1 } };
    
    // If we're at max attempts and not locked, lock the account
    if (this.loginAttempts + 1 >= maxAttempts && !this.isLocked) {
        updates.$set = { lockUntil: Date.now() + lockTime };
    }
    
    return this.updateOne(updates);
};

// Method to reset login attempts
adminSchema.methods.resetLoginAttempts = function() {
    return this.updateOne({
        $unset: { loginAttempts: 1, lockUntil: 1 },
        $set: { lastLogin: new Date() }
    });
};

// Static method to find admin by encrypted email
adminSchema.statics.findByEmail = async function(email) {
    try {
        // Check if database is connected
        if (mongoose.connection.readyState !== 1) {
            console.log('⚠️ Database not connected, skipping email check');
            return null;
        }

        const encryptedEmail = CryptoJS.AES.encrypt(email.toLowerCase(), process.env.AES_SECRET_KEY).toString();
        return await this.findOne({ email: encryptedEmail });
    } catch (error) {
        console.error('❌ Error finding admin by email:', error.message);
        // Return null instead of throwing error to allow signup to continue
        return null;
    }
};

// Index for better performance
adminSchema.index({ email: 1 });
adminSchema.index({ createdAt: 1 });

module.exports = mongoose.model('Admin', adminSchema);
