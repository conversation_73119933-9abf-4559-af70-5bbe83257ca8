# 📞 Contact Management System - Complete Documentation

## Overview
The Contact Management System provides comprehensive functionality for handling customer inquiries, lead management, and communication tracking. The system includes public contact submission and admin management capabilities with email notifications and analytics.

## 🏗️ Architecture

### Core Components
- **Model**: `models/contact.model.js` - Contact data structure
- **Service**: `services/contact.service.js` - Business logic
- **Controller**: `controllers/contact.controller.js` - Request handling
- **Routes**: `routes/contact.route.js` - API endpoints
- **Email Service**: `services/email.service.js` - Email notifications

### Key Features
- ✅ **Lead Management** - Status tracking and priority management
- ✅ **Email Notifications** - Automatic user and admin notifications
- ✅ **Advanced Filtering** - Search, pagination, status filtering
- ✅ **Analytics Dashboard** - Contact statistics and insights
- ✅ **IST Timezone Support** - Indian Standard Time formatting
- ✅ **Phone Validation** - International phone number support
- ✅ **Service Integration** - Links to service offerings

## 📊 Database Schema

### Contact Model
```javascript
{
  _id: ObjectId,
  name: String (required, max 100 chars),
  email: String (required, validated, lowercase),
  countryCode: String (required, default: "+91"),
  phoneNumber: String (required, 8-15 digits),
  phone: String (virtual field: countryCode + phoneNumber),
  service: String (required),
  message: String (required, max 1000 chars),
  status: String (enum: "new", "in-progress", "resolved", "closed"),
  priority: String (enum: "low", "medium", "high", "urgent"),
  createdAt: Date (IST formatted),
  updatedAt: Date (IST formatted)
}
```

### Database Indexes
```javascript
// Performance indexes
{ email: 1 }
{ status: 1 }
{ service: 1 }
{ createdAt: -1 }
```

## 🔗 API Endpoints

### Public APIs (No Authentication Required)

#### 1. Create Contact
```http
POST /contacts
Content-Type: application/json
```

**Request Body:**
```javascript
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "countryCode": "+91",
  "phoneNumber": "9876543210",
  "service": "Wedding Planning",
  "message": "I need help planning my wedding for 200 guests",
  "status": "new",        // Optional: new, in-progress, resolved, closed
  "priority": "medium"    // Optional: low, medium, high, urgent
}
```

**Response:**
```javascript
{
  "status": true,
  "code": 201,
  "message": "Contact created successfully",
  "data": {
    "_id": "ObjectId",
    "name": "John Doe",
    "email": "<EMAIL>",
    "countryCode": "+91",
    "phoneNumber": "9876543210",
    "phone": "+919876543210",
    "service": "Wedding Planning",
    "message": "I need help planning my wedding for 200 guests",
    "status": "new",
    "priority": "medium",
    "createdAt": "01/01/2024, 10:30:00",
    "updatedAt": "01/01/2024, 10:30:00"
  }
}
```

**Email Notifications:**
- User receives confirmation email
- Admin receives notification email
- Both emails are sent automatically

### Admin APIs (Authentication Required)

#### 2. Get All Contacts
```http
GET /contacts?page=1&limit=10&status=new&service=Wedding&priority=high&search=john&sortBy=createdAt&sortOrder=desc
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Contacts per page (default: 10)
- `status`: Filter by status (new, in-progress, resolved, closed)
- `service`: Filter by service type
- `priority`: Filter by priority (low, medium, high, urgent)
- `search`: Search in name, email, phone, message
- `sortBy`: Sort field (default: createdAt)
- `sortOrder`: asc/desc (default: desc)

**Response:**
```javascript
{
  "status": true,
  "code": 200,
  "message": "Contacts retrieved successfully",
  "data": [
    {
      "_id": "ObjectId",
      "name": "John Doe",
      "email": "<EMAIL>",
      "countryCode": "+91",
      "phoneNumber": "9876543210",
      "phone": "+919876543210",
      "service": "Wedding Planning",
      "message": "I need help planning my wedding",
      "status": "new",
      "priority": "medium",
      "createdAt": "01/01/2024, 10:30:00",
      "updatedAt": "01/01/2024, 10:30:00"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalContacts": 50,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

#### 3. Get Contact by ID
```http
GET /contacts/:id
```

**Response:**
```javascript
{
  "status": true,
  "code": 200,
  "message": "Contact retrieved successfully",
  "data": {
    // Full contact object
  }
}
```

#### 4. Update Contact
```http
PUT /contacts/:id
Content-Type: application/json
```

**Request Body:**
```javascript
{
  "status": "in-progress",
  "priority": "high",
  "message": "Updated message content"
}
```

**Response:**
```javascript
{
  "status": true,
  "code": 200,
  "message": "Contact updated successfully",
  "data": {
    // Updated contact object
  }
}
```

#### 5. Delete Contact
```http
DELETE /contacts/:id
```

**Response:**
```javascript
{
  "status": true,
  "code": 200,
  "message": "Contact deleted successfully",
  "data": null
}
```

#### 6. Get Contact Statistics
```http
GET /contacts/statistics
```

**Response:**
```javascript
{
  "status": true,
  "code": 200,
  "message": "Contact statistics retrieved successfully",
  "data": {
    "totalContacts": 150,
    "statusStats": [
      { "_id": "new", "count": 45 },
      { "_id": "in-progress", "count": 30 },
      { "_id": "resolved", "count": 60 },
      { "_id": "closed", "count": 15 }
    ],
    "serviceStats": [
      { "_id": "Wedding Planning", "count": 80 },
      { "_id": "Corporate Events", "count": 40 },
      { "_id": "Birthday Parties", "count": 30 }
    ],
    "priorityStats": [
      { "_id": "low", "count": 20 },
      { "_id": "medium", "count": 80 },
      { "_id": "high", "count": 40 },
      { "_id": "urgent", "count": 10 }
    ]
  }
}
```

#### 7. Get Contacts by Status
```http
GET /contacts/status/:status?page=1&limit=10&sortBy=createdAt&sortOrder=desc
```

#### 8. Get Contacts by Service
```http
GET /contacts/service/:service?page=1&limit=10&sortBy=createdAt&sortOrder=desc
```

## 📧 Email Notification System

### User Confirmation Email
- **Subject**: "Thank You for Reaching Out to [Company Name]!"
- **Content**: Professional thank you message with contact details summary
- **Timing**: Sent immediately after contact creation

### Admin Notification Email
- **Subject**: "🚨 New Contact Form Submission - [Service Type]"
- **Content**: Detailed contact information with action required notice
- **Timing**: Sent immediately after contact creation
- **Action Required**: Contact customer within 24 hours

### Email Template Features
- **Professional HTML Design**
- **Contact Details Summary**
- **IST Timestamp**
- **Service-specific Information**
- **Call-to-action for Admin**

## 🔄 Lead Management Workflow

### 1. Contact Submission Flow
```
User submits contact → Status: "new" → Priority: "medium" → Email notifications sent
```

### 2. Admin Management Flow
```
Admin reviews contact → Updates status → Changes priority → Tracks progress
```

### Status Lifecycle
- **new**: Initial submission, awaiting review
- **in-progress**: Admin is working on the inquiry
- **resolved**: Issue/inquiry has been addressed
- **closed**: Contact is complete, no further action needed

### Priority Levels
- **low**: Non-urgent inquiries
- **medium**: Standard inquiries (default)
- **high**: Important inquiries requiring quick response
- **urgent**: Critical inquiries requiring immediate attention

## 🛡️ Security & Validation

### Input Validation
- **Name**: Required, max 100 characters, trimmed
- **Email**: Required, valid email format, lowercase
- **Phone**: Required, 8-15 digits, international format support
- **Country Code**: Required, default "+91"
- **Service**: Required, dropdown validation
- **Message**: Required, max 1000 characters

### Data Sanitization
- **Email**: Converted to lowercase and trimmed
- **Phone**: Spaces and hyphens allowed, validated format
- **Text Fields**: Trimmed whitespace
- **XSS Protection**: Input sanitization

### Phone Number Validation
```javascript
// Regex pattern for international phone numbers
/^[\d\s\-]{8,15}$/

// Supports formats:
// 9876543210
// ************
// ************
```

## 📈 Analytics & Reporting

### Contact Statistics Dashboard
- **Total Contacts**: Overall count across all time
- **Status Distribution**: Breakdown by status
- **Service Distribution**: Popular services analysis
- **Priority Distribution**: Urgency level analysis

### Performance Metrics
- **Response Time**: Time to first contact
- **Resolution Rate**: Percentage of resolved contacts
- **Service Popularity**: Most requested services
- **Lead Conversion**: Contact to customer conversion

## 🔍 Search & Filtering

### Search Functionality
- **Multi-field Search**: Name, email, phone, message
- **Case-insensitive**: Flexible search matching
- **Regex-based**: Partial matching support

### Advanced Filtering
- **Status Filtering**: Filter by contact status
- **Service Filtering**: Filter by service type
- **Priority Filtering**: Filter by priority level
- **Date Range**: Via sorting options

### Pagination
- **Configurable Page Sizes**
- **Complete Pagination Metadata**
- **Navigation Helpers**

## 💡 Frontend Integration Examples

### Contact Form Submission
```javascript
const submitContact = async (contactData) => {
  try {
    const response = await fetch('/contacts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(contactData)
    });
    
    const result = await response.json();
    if (result.status) {
      alert('Thank you! We will contact you soon.');
    }
    return result;
  } catch (error) {
    console.error('Contact submission failed:', error);
  }
};
```

### Admin Dashboard
```javascript
// Get contacts with filtering
const getContacts = async (filters = {}) => {
  const queryParams = new URLSearchParams(filters);
  const response = await fetch(`/contacts?${queryParams}`);
  return response.json();
};

// Update contact status
const updateContactStatus = async (contactId, status, priority) => {
  const response = await fetch(`/contacts/${contactId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ status, priority })
  });
  return response.json();
};
```

### Statistics Dashboard
```javascript
const getContactStats = async () => {
  const response = await fetch('/contacts/statistics');
  const data = await response.json();
  
  // Use data for charts and analytics
  return data;
};
```

## 🚀 Best Practices

### For Developers
1. **Always validate input** on both client and server side
2. **Use pagination** for large contact lists
3. **Implement rate limiting** to prevent spam
4. **Cache statistics** for better performance
5. **Monitor email delivery** status

### For Admins
1. **Respond quickly** to new contacts (within 24 hours)
2. **Update status regularly** to track progress
3. **Set appropriate priorities** based on urgency
4. **Monitor statistics** for business insights
5. **Follow up** on resolved contacts

### For Users
1. **Provide accurate information** in contact forms
2. **Be specific** in service requirements
3. **Include relevant details** in messages
4. **Use valid email addresses** for responses

## 🔧 Configuration

### Environment Variables
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
```

### Database Indexes
```javascript
// Create performance indexes
db.contacts.createIndex({ email: 1 });
db.contacts.createIndex({ status: 1 });
db.contacts.createIndex({ service: 1 });
db.contacts.createIndex({ createdAt: -1 });
```

### IST Timezone Configuration
- All timestamps automatically converted to Indian Standard Time
- Format: DD/MM/YYYY, HH:MM:SS
- Consistent across all API responses

This documentation provides complete coverage of the Contact Management system with all features, endpoints, and implementation details for easy website integration.
